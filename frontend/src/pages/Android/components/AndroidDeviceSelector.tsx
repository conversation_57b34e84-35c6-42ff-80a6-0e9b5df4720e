import React, { useState, useEffect } from 'react';
import { Select, Button, Space, Tag, message, Tooltip } from 'antd';
import { ReloadOutlined, MobileOutlined, WifiOutlined, UsbOutlined } from '@ant-design/icons';

const { Option } = Select;

interface AndroidDevice {
  device_id: string;
  device_name?: string;
  device_model?: string;
  manufacturer?: string;
  android_version?: string;
  screen_resolution?: string;
  connection_type: string;
  status: string;
}

interface AndroidDeviceSelectorProps {
  value?: string;
  onChange?: (deviceId: string | null) => void;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
}

const AndroidDeviceSelector: React.FC<AndroidDeviceSelectorProps> = ({
  value,
  onChange,
  placeholder = "选择Android设备",
  allowClear = true,
  disabled = false
}) => {
  const [devices, setDevices] = useState<AndroidDevice[]>([]);
  const [loading, setLoading] = useState(false);
  const [scanning, setScanning] = useState(false);

  // 获取设备列表
  const fetchDevices = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/v1/android/devices/list');
      
      if (response.ok) {
        const data = await response.json();
        setDevices(data.data || []);
      } else {
        message.error('获取设备列表失败');
      }
    } catch (error) {
      console.error('获取设备列表失败:', error);
      message.error('获取设备列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 扫描设备
  const handleScanDevices = async () => {
    try {
      setScanning(true);
      const response = await fetch('/api/v1/android/devices/scan', {
        method: 'GET'
      });
      
      if (response.ok) {
        const data = await response.json();
        setDevices(data.data || []);
        message.success(data.message || '设备扫描完成');
      } else {
        const errorData = await response.json();
        message.error(`设备扫描失败: ${errorData.detail}`);
      }
    } catch (error) {
      console.error('扫描设备失败:', error);
      message.error('设备扫描失败');
    } finally {
      setScanning(false);
    }
  };

  // 组件挂载时获取设备列表
  useEffect(() => {
    fetchDevices();
  }, []);

  // 渲染连接类型图标
  const renderConnectionIcon = (type: string) => {
    switch (type) {
      case 'usb':
        return <UsbOutlined style={{ color: '#1890ff' }} />;
      case 'wifi':
        return <WifiOutlined style={{ color: '#52c41a' }} />;
      case 'emulator':
        return <MobileOutlined style={{ color: '#722ed1' }} />;
      default:
        return <MobileOutlined />;
    }
  };

  // 渲染设备状态标签
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      online: { color: 'success', text: '在线' },
      offline: { color: 'default', text: '离线' },
      unauthorized: { color: 'warning', text: '未授权' },
      error: { color: 'error', text: '错误' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.offline;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染设备选项
  const renderDeviceOption = (device: AndroidDevice) => {
    const isOnline = device.status === 'online';
    
    return (
      <Option 
        key={device.device_id} 
        value={device.device_id}
        disabled={!isOnline}
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            {renderConnectionIcon(device.connection_type)}
            <span>
              <strong>{device.device_name || device.device_model || device.device_id}</strong>
              {device.manufacturer && (
                <span style={{ color: '#666', marginLeft: '8px' }}>
                  ({device.manufacturer})
                </span>
              )}
            </span>
          </Space>
          <Space>
            {device.android_version && (
              <Tag size="small">Android {device.android_version}</Tag>
            )}
            {renderStatusTag(device.status)}
          </Space>
        </div>
        {device.screen_resolution && (
          <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
            分辨率: {device.screen_resolution}
          </div>
        )}
      </Option>
    );
  };

  const onlineDevices = devices.filter(device => device.status === 'online');
  const offlineDevices = devices.filter(device => device.status !== 'online');

  return (
    <Space.Compact style={{ width: '100%' }}>
      <Select
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        allowClear={allowClear}
        disabled={disabled}
        loading={loading}
        style={{ flex: 1 }}
        optionLabelProp="label"
        notFoundContent={
          devices.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
              <MobileOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
              <div>未发现设备</div>
              <div style={{ fontSize: '12px' }}>请确保设备已连接并开启USB调试</div>
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
              <div>没有在线设备</div>
            </div>
          )
        }
      >
        {onlineDevices.length > 0 && (
          <Select.OptGroup label="在线设备">
            {onlineDevices.map(renderDeviceOption)}
          </Select.OptGroup>
        )}
        
        {offlineDevices.length > 0 && (
          <Select.OptGroup label="离线设备">
            {offlineDevices.map(renderDeviceOption)}
          </Select.OptGroup>
        )}
      </Select>
      
      <Tooltip title="扫描设备">
        <Button 
          icon={<ReloadOutlined />}
          onClick={handleScanDevices}
          loading={scanning}
          disabled={disabled}
        />
      </Tooltip>
    </Space.Compact>
  );
};

export default AndroidDeviceSelector;
