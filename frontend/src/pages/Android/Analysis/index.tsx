import React, { useState, useRef, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Upload,
  Input,
  Select,
  Form,
  message,
  Typography,
  Space,
  Divider,
  Alert,
  Progress
} from 'antd';
import { 
  UploadOutlined, 
  MobileOutlined, 
  PlayCircleOutlined,
  CameraOutlined,
  FileImageOutlined
} from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import AndroidAnalysisResults from './AndroidAnalysisResults';
import AndroidDeviceSelector from '../components/AndroidDeviceSelector';

const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface AnalysisFormData {
  testDescription: string;
  packageName?: string;
  activityName?: string;
  deviceId?: string;
  generateFormats: string[];
  includeNonInteractive: boolean;
  focusAreas?: string[];
  additionalContext?: string;
}

const AndroidAnalysisPage: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [analysisSessionId, setAnalysisSessionId] = useState<string | null>(null);
  const [uploadedFile, setUploadedFile] = useState<UploadFile | null>(null);
  const [screenshotData, setScreenshotData] = useState<string | null>(null);
  const [selectedDevice, setSelectedDevice] = useState<string | null>(null);

  // 分析进度状态
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState<string>('');
  const [analysisLog, setAnalysisLog] = useState<string>('');

  // 使用ref跟踪分析完成状态
  const analysisCompletedRef = useRef(false);

  // 处理文件上传
  const handleFileUpload = (file: UploadFile) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setScreenshotData(result.split(',')[1]); // 移除data:image/...;base64,前缀
      setUploadedFile(file);
    };
    reader.readAsDataURL(file as any);
    return false; // 阻止自动上传
  };

  // 从设备捕获截图
  const handleCaptureScreenshot = async () => {
    if (!selectedDevice) {
      message.error('请先选择设备');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(`/api/v1/android/devices/${selectedDevice}/screenshot`, {
        method: 'POST'
      });
      
      if (response.ok) {
        const data = await response.json();
        setScreenshotData(data.data.screenshot_data);
        setUploadedFile({
          uid: 'captured',
          name: `screenshot_${Date.now()}.png`,
          status: 'done'
        } as UploadFile);
        message.success('截图捕获成功');
      } else {
        message.error('截图捕获失败');
      }
    } catch (error) {
      console.error('捕获截图失败:', error);
      message.error('截图捕获失败');
    } finally {
      setLoading(false);
    }
  };

  // 开始分析
  const handleStartAnalysis = useCallback(async (values: AnalysisFormData) => {
    if (!screenshotData) {
      message.error('请先上传截图或从设备捕获截图');
      return;
    }

    try {
      setLoading(true);
      setIsAnalyzing(true);
      setAnalysisProgress(0);
      setCurrentStep('准备分析...');
      setAnalysisLog('');
      analysisCompletedRef.current = false;

      const sessionId = `android_analysis_${Date.now()}`;

      const requestData = {
        session_id: sessionId,
        screenshot_data: screenshotData,
        device_id: selectedDevice,
        package_name: values.packageName,
        activity_name: values.activityName,
        test_description: values.testDescription,
        additional_context: values.additionalContext,
        generate_formats: values.generateFormats,
        include_non_interactive: values.includeNonInteractive,
        focus_areas: values.focusAreas
      };

      setCurrentStep('启动分析任务...');
      setAnalysisProgress(10);

      const response = await fetch('/api/v1/android/analysis/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      if (response.ok) {
        const data = await response.json();
        setAnalysisSessionId(data.session_id);

        setCurrentStep('建立连接...');
        setAnalysisProgress(20);

        // 建立SSE连接监听分析进度
        const sseEndpoint = `/api/v1/android/analysis/stream/${data.session_id}`;
        console.log('使用SSE端点:', sseEndpoint);

        const eventSource = new EventSource(sseEndpoint);

        eventSource.onopen = () => {
          console.log('SSE连接已建立');
          setAnalysisLog(prev => prev + '✅ 连接已建立\n');
          setCurrentStep('AI正在分析...');
          setAnalysisProgress(30);
        };

        eventSource.addEventListener('connected', () => {
          console.log('已连接到Android分析流');
          setAnalysisLog(prev => prev + '🤖 Android分析智能体已启动\n');
        });

        eventSource.addEventListener('progress', (event) => {
          try {
            if (event.data && event.data !== 'undefined') {
              const data = JSON.parse(event.data);
              console.log('分析进度:', data);

              if (data.content) {
                setAnalysisLog(prev => prev + data.content);
                setAnalysisProgress(prev => Math.min(prev + 5, 85));
              }

              if (data.step) {
                setCurrentStep(data.step);
              }
            }
          } catch (e) {
            console.error('解析SSE消息失败:', e);
          }
        });

        eventSource.addEventListener('completed', (event) => {
          try {
            if (event.data && event.data !== 'undefined') {
              const data = JSON.parse(event.data);
              console.log('分析完成:', data);
              setAnalysisLog(prev => prev + '\n✅ ' + (data.content || '分析完成') + '\n');
              setCurrentStep('分析完成');
              setAnalysisProgress(100);
              analysisCompletedRef.current = true;
            } else {
              console.log('分析完成，无具体数据');
              setAnalysisLog(prev => prev + '\n✅ 分析完成\n');
              setCurrentStep('分析完成');
              setAnalysisProgress(100);
              analysisCompletedRef.current = true;
            }
          } catch (e) {
            console.error('解析完成事件失败:', e);
          }

          eventSource.close();
          setIsAnalyzing(false);
          setLoading(false);
        });

        eventSource.onerror = (error) => {
          console.error('SSE连接错误:', error);
          setAnalysisLog(prev => prev + '\n❌ 连接错误，请重试\n');
          setCurrentStep('连接错误');
          eventSource.close();
          setIsAnalyzing(false);
          setLoading(false);
        };

        message.success('分析已启动，请查看进度面板');
      } else {
        const errorData = await response.json();
        message.error(`分析启动失败: ${errorData.detail}`);
        setIsAnalyzing(false);
        setLoading(false);
      }
    } catch (error) {
      console.error('启动分析失败:', error);
      message.error('分析启动失败');
      setIsAnalyzing(false);
      setLoading(false);
    }
  }, [screenshotData, selectedDevice]);

  // 清空分析状态
  const handleClearAnalysis = useCallback(() => {
    setAnalysisLog('');
    setAnalysisProgress(0);
    setCurrentStep('');
    setIsAnalyzing(false);
    analysisCompletedRef.current = false;
  }, []);

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <MobileOutlined style={{ marginRight: '12px', color: '#1890ff' }} />
          Android界面分析
        </Title>
        <Paragraph>
          上传Android应用截图或从连接的设备捕获截图，AI将智能分析界面元素并生成自动化测试脚本
        </Paragraph>
      </div>

      <Row gutter={[24, 24]}>
        {/* 左侧：分析配置 */}
        <Col xs={24} lg={12}>
          <Card title="分析配置" style={{ height: 'fit-content' }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleStartAnalysis}
              initialValues={{
                testDescription: 'Android界面分析',
                generateFormats: ['appium'],
                includeNonInteractive: false
              }}
            >
              {/* 设备选择 */}
              <Form.Item label="选择设备">
                <AndroidDeviceSelector
                  value={selectedDevice}
                  onChange={setSelectedDevice}
                  placeholder="选择要分析的Android设备"
                />
              </Form.Item>

              {/* 截图上传/捕获 */}
              <Form.Item label="应用截图">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Space>
                    <Upload
                      accept="image/*"
                      beforeUpload={handleFileUpload}
                      showUploadList={false}
                      maxCount={1}
                    >
                      <Button icon={<UploadOutlined />}>
                        上传截图
                      </Button>
                    </Upload>
                    
                    <Button 
                      icon={<CameraOutlined />}
                      onClick={handleCaptureScreenshot}
                      disabled={!selectedDevice}
                      loading={loading}
                    >
                      从设备捕获
                    </Button>
                  </Space>
                  
                  {uploadedFile && (
                    <Alert
                      message={`已选择文件: ${uploadedFile.name}`}
                      type="success"
                      showIcon
                      icon={<FileImageOutlined />}
                    />
                  )}
                </Space>
              </Form.Item>

              <Divider />

              {/* 应用信息 */}
              <Form.Item
                name="packageName"
                label="应用包名"
                tooltip="Android应用的包名，如com.example.app"
              >
                <Input placeholder="com.example.app" />
              </Form.Item>

              <Form.Item
                name="activityName"
                label="Activity名称"
                tooltip="当前界面的Activity名称"
              >
                <Input placeholder="MainActivity" />
              </Form.Item>

              {/* 测试描述 */}
              <Form.Item
                name="testDescription"
                label="测试描述"
                rules={[{ required: true, message: '请输入测试描述' }]}
              >
                <Input placeholder="描述这次分析的目的" />
              </Form.Item>

              {/* 生成格式 */}
              <Form.Item
                name="generateFormats"
                label="生成脚本格式"
                tooltip="选择要生成的自动化脚本格式"
              >
                <Select mode="multiple" placeholder="选择脚本格式">
                  <Option value="appium">Appium (Python)</Option>
                  <Option value="uiautomator2">UIAutomator2</Option>
                  <Option value="midscene">Midscene</Option>
                </Select>
              </Form.Item>

              {/* 高级选项 */}
              <Form.Item
                name="includeNonInteractive"
                valuePropName="checked"
                tooltip="是否包含不可交互的元素（如文本、图片等）"
              >
                <input type="checkbox" style={{ marginRight: '8px' }} />
                包含非交互元素
              </Form.Item>

              <Form.Item
                name="additionalContext"
                label="额外上下文"
                tooltip="提供额外的上下文信息帮助AI更好地理解界面"
              >
                <TextArea 
                  rows={3} 
                  placeholder="描述应用的功能、当前界面的作用等..."
                />
              </Form.Item>

              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={loading}
                  disabled={!screenshotData}
                  icon={<PlayCircleOutlined />}
                  size="large"
                  block
                >
                  开始分析
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 右侧：分析进度和结果 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <span>分析进度</span>
                {(isAnalyzing || analysisLog) && (
                  <Button
                    size="small"
                    type="link"
                    onClick={handleClearAnalysis}
                    style={{ fontSize: 12, padding: '0 4px', height: 'auto' }}
                  >
                    清空日志
                  </Button>
                )}
              </Space>
            }
            style={{ height: '600px', marginBottom: '24px' }}
          >
            <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* 进度条 */}
              {(isAnalyzing || analysisProgress > 0) && (
                <div style={{ marginBottom: 16, flexShrink: 0 }}>
                  <Progress
                    percent={analysisProgress}
                    size="small"
                    status={isAnalyzing ? (analysisProgress === 100 ? "success" : "active") : "success"}
                    showInfo={true}
                    format={(percent) => `${percent}%`}
                  />
                  {currentStep && (
                    <Text type="secondary" style={{ fontSize: 12, marginTop: 4, display: 'block' }}>
                      {currentStep}
                    </Text>
                  )}
                </div>
              )}

              {/* 分析日志 */}
              {analysisLog ? (
                <div style={{
                  flex: 1,
                  backgroundColor: '#f5f5f5',
                  padding: '12px',
                  borderRadius: '6px',
                  overflow: 'auto',
                  fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                  fontSize: '12px',
                  lineHeight: '1.4',
                  whiteSpace: 'pre-wrap'
                }}>
                  {analysisLog}
                </div>
              ) : (
                <div style={{
                  flex: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexDirection: 'column',
                  color: '#999'
                }}>
                  <MobileOutlined style={{ fontSize: '64px', marginBottom: '16px' }} />
                  <Text>请配置分析参数并开始分析</Text>
                </div>
              )}
            </div>
          </Card>

          {/* 分析结果 */}
          {analysisSessionId && (
            <AndroidAnalysisResults sessionId={analysisSessionId} />
          )}
        </Col>
      </Row>
    </div>
  );
};

export default AndroidAnalysisPage;
