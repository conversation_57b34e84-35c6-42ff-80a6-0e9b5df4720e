import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Spin, 
  Progress, 
  Typography, 
  Alert, 
  Tabs, 
  List, 
  Tag, 
  Button,
  Space,
  Collapse,
  Descriptions,
  message
} from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  CodeOutlined,
  MobileOutlined,
  DownloadOutlined,
  CopyOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;

interface AndroidAnalysisResultsProps {
  sessionId: string;
}

interface AnalysisMessage {
  type: string;
  message: string;
  progress?: number;
  result?: any;
  error?: string;
}

interface UIElement {
  id: string;
  element_name?: string;
  element_type: string;
  element_description: string;
  resource_id?: string;
  class_name?: string;
  text?: string;
  content_desc?: string;
  bounds?: string;
  clickable: boolean;
  enabled: boolean;
  focusable: boolean;
  scrollable: boolean;
  confidence_score: number;
  is_testable: boolean;
}

interface GeneratedScript {
  script_type: string;
  script_language: string;
  script_content: string;
  test_description: string;
  dependencies: string[];
  setup_instructions?: string;
  execution_notes?: string;
}

const AndroidAnalysisResults: React.FC<AndroidAnalysisResultsProps> = ({ sessionId }) => {
  const [loading, setLoading] = useState(true);
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState<'processing' | 'completed' | 'failed'>('processing');
  const [currentMessage, setCurrentMessage] = useState<string>('');
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [generatedScripts, setGeneratedScripts] = useState<GeneratedScript[]>([]);
  const [uiElements, setUIElements] = useState<UIElement[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!sessionId) return;

    // 建立SSE连接
    const eventSource = new EventSource(`/api/v1/android/analysis/stream/${sessionId}`);

    eventSource.onmessage = (event) => {
      try {
        const data: AnalysisMessage = JSON.parse(event.data);
        
        switch (data.type) {
          case 'progress':
            setProgress(data.progress || 0);
            setCurrentMessage(data.message);
            break;
            
          case 'complete':
            setLoading(false);
            setStatus('completed');
            setProgress(100);
            setCurrentMessage(data.message);
            
            if (data.result) {
              setAnalysisResult(data.result.analysis_result);
              setGeneratedScripts(data.result.generated_scripts || []);
              
              // 提取UI元素
              if (data.result.analysis_result?.ui_elements) {
                setUIElements(data.result.analysis_result.ui_elements);
              }
            }
            break;
            
          case 'error':
            setLoading(false);
            setStatus('failed');
            setError(data.error || data.message);
            setCurrentMessage(data.message);
            break;
            
          case 'heartbeat':
            // 心跳消息，保持连接
            break;
        }
      } catch (err) {
        console.error('解析SSE消息失败:', err);
      }
    };

    eventSource.onerror = (event) => {
      console.error('SSE连接错误:', event);
      setLoading(false);
      setStatus('failed');
      setError('连接中断');
      eventSource.close();
    };

    return () => {
      eventSource.close();
    };
  }, [sessionId]);

  // 复制脚本内容
  const handleCopyScript = (content: string) => {
    navigator.clipboard.writeText(content).then(() => {
      message.success('脚本已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  // 下载脚本
  const handleDownloadScript = (script: GeneratedScript) => {
    const blob = new Blob([script.script_content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${script.test_description}_${script.script_type}.${script.script_language === 'python' ? 'py' : 'js'}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 渲染UI元素类型标签
  const renderElementTypeTag = (type: string) => {
    const colorMap: { [key: string]: string } = {
      'Button': 'blue',
      'EditText': 'green',
      'TextView': 'default',
      'ImageView': 'orange',
      'LinearLayout': 'purple',
      'RelativeLayout': 'purple',
      'FrameLayout': 'purple',
      'RecyclerView': 'cyan',
      'ListView': 'cyan',
      'ScrollView': 'geekblue'
    };
    
    return <Tag color={colorMap[type] || 'default'}>{type}</Tag>;
  };

  // 渲染置信度
  const renderConfidence = (score: number) => {
    const color = score >= 0.8 ? 'success' : score >= 0.6 ? 'warning' : 'error';
    return (
      <Progress 
        percent={Math.round(score * 100)} 
        size="small" 
        status={color === 'error' ? 'exception' : undefined}
        strokeColor={color === 'success' ? '#52c41a' : color === 'warning' ? '#faad14' : undefined}
      />
    );
  };

  if (loading) {
    return (
      <Card title="分析进度" style={{ height: '600px' }}>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: '24px' }}>
            <Progress percent={progress} status="active" />
            <Text style={{ marginTop: '16px', display: 'block' }}>
              {currentMessage}
            </Text>
          </div>
        </div>
      </Card>
    );
  }

  if (status === 'failed') {
    return (
      <Card title="分析结果" style={{ height: '600px' }}>
        <Alert
          message="分析失败"
          description={error || currentMessage}
          type="error"
          showIcon
          icon={<ExclamationCircleOutlined />}
        />
      </Card>
    );
  }

  return (
    <Card title="分析结果" style={{ height: '600px', overflow: 'auto' }}>
      <Alert
        message="分析完成"
        description={currentMessage}
        type="success"
        showIcon
        icon={<CheckCircleOutlined />}
        style={{ marginBottom: '16px' }}
      />

      <Tabs defaultActiveKey="summary">
        <TabPane tab="分析摘要" key="summary">
          {analysisResult && (
            <Space direction="vertical" style={{ width: '100%' }}>
              <Descriptions size="small" column={1}>
                <Descriptions.Item label="应用名称">
                  {analysisResult.app_name || '未知'}
                </Descriptions.Item>
                <Descriptions.Item label="包名">
                  {analysisResult.package_name || '未知'}
                </Descriptions.Item>
                <Descriptions.Item label="Activity">
                  {analysisResult.activity_name || '未知'}
                </Descriptions.Item>
                <Descriptions.Item label="识别元素数量">
                  {uiElements.length} 个
                </Descriptions.Item>
                <Descriptions.Item label="整体置信度">
                  {renderConfidence(analysisResult.confidence_score || 0)}
                </Descriptions.Item>
              </Descriptions>
              
              <div>
                <Title level={5}>界面描述</Title>
                <Paragraph>{analysisResult.screen_description}</Paragraph>
              </div>
              
              {analysisResult.test_scenarios && analysisResult.test_scenarios.length > 0 && (
                <div>
                  <Title level={5}>建议测试场景</Title>
                  <List
                    size="small"
                    dataSource={analysisResult.test_scenarios}
                    renderItem={(scenario: string) => (
                      <List.Item>• {scenario}</List.Item>
                    )}
                  />
                </div>
              )}
            </Space>
          )}
        </TabPane>

        <TabPane tab={`UI元素 (${uiElements.length})`} key="elements">
          <List
            size="small"
            dataSource={uiElements}
            renderItem={(element: UIElement) => (
              <List.Item>
                <List.Item.Meta
                  title={
                    <Space>
                      {renderElementTypeTag(element.element_type)}
                      <Text strong>{element.element_name || element.resource_id || '未命名元素'}</Text>
                      {element.is_testable && <Tag color="green">可测试</Tag>}
                    </Space>
                  }
                  description={
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <Text>{element.element_description}</Text>
                      {element.text && <Text type="secondary">文本: {element.text}</Text>}
                      {element.resource_id && <Text type="secondary">ID: {element.resource_id}</Text>}
                      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                        {element.clickable && <Tag size="small">可点击</Tag>}
                        {element.focusable && <Tag size="small">可聚焦</Tag>}
                        {element.scrollable && <Tag size="small">可滚动</Tag>}
                        {!element.enabled && <Tag size="small" color="red">已禁用</Tag>}
                      </div>
                      {renderConfidence(element.confidence_score)}
                    </Space>
                  }
                />
              </List.Item>
            )}
          />
        </TabPane>

        <TabPane tab={`生成脚本 (${generatedScripts.length})`} key="scripts">
          {generatedScripts.length > 0 ? (
            <Collapse>
              {generatedScripts.map((script, index) => (
                <Panel 
                  header={
                    <Space>
                      <CodeOutlined />
                      <Text strong>{script.script_type.toUpperCase()}</Text>
                      <Tag>{script.script_language}</Tag>
                    </Space>
                  } 
                  key={index}
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text strong>测试描述: </Text>
                      <Text>{script.test_description}</Text>
                    </div>
                    
                    {script.dependencies.length > 0 && (
                      <div>
                        <Text strong>依赖包: </Text>
                        <Text code>{script.dependencies.join(', ')}</Text>
                      </div>
                    )}
                    
                    <div style={{ display: 'flex', gap: '8px', marginBottom: '8px' }}>
                      <Button 
                        size="small" 
                        icon={<CopyOutlined />}
                        onClick={() => handleCopyScript(script.script_content)}
                      >
                        复制
                      </Button>
                      <Button 
                        size="small" 
                        icon={<DownloadOutlined />}
                        onClick={() => handleDownloadScript(script)}
                      >
                        下载
                      </Button>
                    </div>
                    
                    <pre style={{ 
                      backgroundColor: '#f5f5f5', 
                      padding: '12px', 
                      borderRadius: '4px',
                      fontSize: '12px',
                      overflow: 'auto',
                      maxHeight: '300px'
                    }}>
                      {script.script_content}
                    </pre>
                    
                    {script.setup_instructions && (
                      <Alert
                        message="设置说明"
                        description={script.setup_instructions}
                        type="info"
                        size="small"
                      />
                    )}
                    
                    {script.execution_notes && (
                      <Alert
                        message="执行注意事项"
                        description={script.execution_notes}
                        type="warning"
                        size="small"
                      />
                    )}
                  </Space>
                </Panel>
              ))}
            </Collapse>
          ) : (
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              <CodeOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <div>未生成脚本</div>
            </div>
          )}
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default AndroidAnalysisResults;
