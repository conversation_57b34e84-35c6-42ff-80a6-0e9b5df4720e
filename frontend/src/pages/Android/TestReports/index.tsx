import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Select,
  Input,
  DatePicker,
  Row,
  Col,
  Statistic,
  Typography,
  Empty,
  Spin,
  message,
  Drawer,
  Descriptions,
  Alert,
  Badge,
  Divider,
  Progress,
  Tooltip,
  Chart
} from 'antd';
import {
  FileTextOutlined,
  EyeOutlined,
  DownloadOutlined,
  ReloadOutlined,
  SearchOutlined,
  CalendarOutlined,
  Bar<PERSON>hartOutlined,
  FilterOutlined,
  ExportOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  PlayCircleOutlined,
  MobileOutlined,
  PieChartOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import dayjs from 'dayjs';
import { getAndroidExecutionHistory } from '../../../services/androidApi';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Search } = Input;

interface TestReport {
  id: string;
  report_name: string;
  report_type: 'summary' | 'detailed' | 'comparison';
  execution_ids: string[];
  device_ids: string[];
  date_range: [string, string];
  total_executions: number;
  successful_executions: number;
  failed_executions: number;
  success_rate: number;
  average_duration: number;
  generated_at: string;
  report_data?: any;
}

const AndroidTestReports: React.FC = () => {
  const [reports, setReports] = useState<TestReport[]>([]);
  const [executions, setExecutions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [generateLoading, setGenerateLoading] = useState(false);
  const [selectedReport, setSelectedReport] = useState<TestReport | null>(null);
  const [reportVisible, setReportVisible] = useState(false);
  const [generateModalVisible, setGenerateModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [reportTypeFilter, setReportTypeFilter] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  // 获取执行历史数据
  const fetchExecutions = async () => {
    try {
      const response = await getAndroidExecutionHistory();
      setExecutions(response.data);
    } catch (error) {
      message.error('获取执行数据失败');
    }
  };

  // 生成模拟报告数据
  const generateMockReports = () => {
    const mockReports: TestReport[] = [
      {
        id: '1',
        report_name: '本周Android测试总结报告',
        report_type: 'summary',
        execution_ids: ['exec1', 'exec2', 'exec3'],
        device_ids: ['device1', 'device2'],
        date_range: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        total_executions: 15,
        successful_executions: 12,
        failed_executions: 3,
        success_rate: 80,
        average_duration: 245,
        generated_at: dayjs().subtract(1, 'hour').toISOString()
      },
      {
        id: '2',
        report_name: '设备兼容性测试报告',
        report_type: 'comparison',
        execution_ids: ['exec4', 'exec5', 'exec6'],
        device_ids: ['device1', 'device2', 'device3'],
        date_range: [dayjs().subtract(3, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        total_executions: 9,
        successful_executions: 7,
        failed_executions: 2,
        success_rate: 78,
        average_duration: 180,
        generated_at: dayjs().subtract(2, 'hour').toISOString()
      }
    ];
    setReports(mockReports);
  };

  useEffect(() => {
    fetchExecutions();
    generateMockReports();
  }, []);

  // 生成新报告
  const handleGenerateReport = async (reportData: any) => {
    try {
      setGenerateLoading(true);
      
      // 模拟生成报告
      const newReport: TestReport = {
        id: `report_${Date.now()}`,
        report_name: reportData.reportName,
        report_type: reportData.reportType,
        execution_ids: reportData.executionIds || [],
        device_ids: reportData.deviceIds || [],
        date_range: reportData.dateRange || [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        total_executions: executions.length,
        successful_executions: executions.filter(e => e.execution_status === 'completed').length,
        failed_executions: executions.filter(e => e.execution_status === 'failed').length,
        success_rate: Math.round((executions.filter(e => e.execution_status === 'completed').length / executions.length) * 100),
        average_duration: Math.round(executions.reduce((sum, e) => sum + (e.duration_seconds || 0), 0) / executions.length),
        generated_at: new Date().toISOString()
      };

      setReports([newReport, ...reports]);
      setGenerateModalVisible(false);
      message.success('报告生成成功');
    } catch (error) {
      message.error('报告生成失败');
    } finally {
      setGenerateLoading(false);
    }
  };

  // 查看报告详情
  const handleViewReport = (report: TestReport) => {
    setSelectedReport(report);
    setReportVisible(true);
  };

  // 下载报告
  const handleDownloadReport = (report: TestReport) => {
    const reportData = {
      ...report,
      generated_time: new Date().toISOString(),
      executions: executions.filter(e => report.execution_ids.includes(e.id))
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `android_test_report_${report.id}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 渲染报告类型标签
  const renderReportType = (type: string) => {
    const typeConfig = {
      summary: { color: 'blue', text: '总结报告' },
      detailed: { color: 'green', text: '详细报告' },
      comparison: { color: 'orange', text: '对比报告' }
    };
    
    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.summary;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 过滤报告
  const filteredReports = reports.filter(report => {
    const matchesSearch = !searchText || 
      report.report_name.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesType = !reportTypeFilter || report.report_type === reportTypeFilter;
    
    const matchesDate = !dateRange || (
      dayjs(report.generated_at).isAfter(dateRange[0]) &&
      dayjs(report.generated_at).isBefore(dateRange[1])
    );

    return matchesSearch && matchesType && matchesDate;
  });

  // 计算总体统计
  const getTotalStatistics = () => {
    const totalExecutions = executions.length;
    const successfulExecutions = executions.filter(e => e.execution_status === 'completed').length;
    const failedExecutions = executions.filter(e => e.execution_status === 'failed').length;
    const successRate = totalExecutions > 0 ? Math.round((successfulExecutions / totalExecutions) * 100) : 0;

    return { totalExecutions, successfulExecutions, failedExecutions, successRate };
  };

  const statistics = getTotalStatistics();

  // 表格列定义
  const columns = [
    {
      title: '报告名称',
      dataIndex: 'report_name',
      key: 'report_name',
      render: (text: string, record: TestReport) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ID: {record.id}
          </Text>
        </div>
      )
    },
    {
      title: '报告类型',
      dataIndex: 'report_type',
      key: 'report_type',
      render: renderReportType
    },
    {
      title: '执行数量',
      dataIndex: 'total_executions',
      key: 'total_executions',
      render: (count: number) => (
        <Badge count={count} style={{ backgroundColor: '#52c41a' }} />
      )
    },
    {
      title: '成功率',
      dataIndex: 'success_rate',
      key: 'success_rate',
      render: (rate: number) => (
        <div>
          <Progress 
            percent={rate} 
            size="small" 
            status={rate >= 80 ? 'success' : rate >= 60 ? 'normal' : 'exception'}
          />
          <Text style={{ fontSize: '12px' }}>{rate}%</Text>
        </div>
      )
    },
    {
      title: '平均时长',
      dataIndex: 'average_duration',
      key: 'average_duration',
      render: (duration: number) => {
        const minutes = Math.floor(duration / 60);
        const seconds = duration % 60;
        return `${minutes}分${seconds}秒`;
      }
    },
    {
      title: '生成时间',
      dataIndex: 'generated_at',
      key: 'generated_at',
      render: (time: string) => (
        <div>
          <div>{dayjs(time).format('MM-DD')}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {dayjs(time).format('HH:mm')}
          </Text>
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: TestReport) => (
        <Space>
          <Button 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleViewReport(record)}
          >
            查看
          </Button>
          <Button 
            size="small" 
            icon={<DownloadOutlined />}
            onClick={() => handleDownloadReport(record)}
          >
            下载
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div style={{ marginBottom: '24px' }}>
          <Title level={2}>
            <FileTextOutlined style={{ marginRight: '12px', color: '#eb2f96' }} />
            Android测试报告
          </Title>
          <Text type="secondary">
            生成和管理Android自动化测试的详细报告
          </Text>
        </div>

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic 
                title="总报告数" 
                value={reports.length} 
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic 
                title="总执行数" 
                value={statistics.totalExecutions} 
                prefix={<PlayCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic 
                title="成功执行" 
                value={statistics.successfulExecutions} 
                valueStyle={{ color: '#3f8600' }}
                prefix={<CheckCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic 
                title="整体成功率" 
                value={statistics.successRate} 
                suffix="%" 
                valueStyle={{ color: statistics.successRate >= 80 ? '#3f8600' : '#cf1322' }}
                prefix={<PieChartOutlined />}
              />
            </Card>
          </Col>
        </Row>

        {/* 过滤器和操作 */}
        <Card style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8}>
              <Search
                placeholder="搜索报告名称"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
            </Col>
            <Col xs={24} sm={6}>
              <Select
                placeholder="报告类型"
                value={reportTypeFilter}
                onChange={setReportTypeFilter}
                allowClear
                style={{ width: '100%' }}
              >
                <Select.Option value="summary">总结报告</Select.Option>
                <Select.Option value="detailed">详细报告</Select.Option>
                <Select.Option value="comparison">对比报告</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={6}>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
              />
            </Col>
            <Col xs={24} sm={4}>
              <Space>
                <Button 
                  type="primary"
                  icon={<ExportOutlined />}
                  onClick={() => setGenerateModalVisible(true)}
                >
                  生成报告
                </Button>
                <Button 
                  icon={<ReloadOutlined />}
                  onClick={() => {
                    fetchExecutions();
                    generateMockReports();
                  }}
                  loading={loading}
                />
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 报告列表 */}
        <Card title="测试报告列表">
          <Table
            columns={columns}
            dataSource={filteredReports}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个报告`
            }}
          />
        </Card>

        {/* 生成报告弹窗 */}
        <Modal
          title="生成测试报告"
          open={generateModalVisible}
          onCancel={() => setGenerateModalVisible(false)}
          onOk={() => {
            handleGenerateReport({
              reportName: `Android测试报告_${dayjs().format('YYYY-MM-DD')}`,
              reportType: 'summary',
              dateRange: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            });
          }}
          confirmLoading={generateLoading}
        >
          <Alert
            message="报告生成"
            description="将基于最近的执行数据生成测试报告，包含成功率、执行时间、设备分布等统计信息。"
            type="info"
            style={{ marginBottom: 16 }}
          />
          <p>报告将包含以下内容：</p>
          <ul>
            <li>执行统计和成功率分析</li>
            <li>设备兼容性报告</li>
            <li>性能指标分析</li>
            <li>错误分析和建议</li>
          </ul>
        </Modal>

        {/* 报告详情抽屉 */}
        <Drawer
          title="报告详情"
          placement="right"
          width={800}
          open={reportVisible}
          onClose={() => setReportVisible(false)}
        >
          {selectedReport && (
            <div>
              <Descriptions column={2} bordered style={{ marginBottom: '24px' }}>
                <Descriptions.Item label="报告名称" span={2}>
                  {selectedReport.report_name}
                </Descriptions.Item>
                <Descriptions.Item label="报告类型">
                  {renderReportType(selectedReport.report_type)}
                </Descriptions.Item>
                <Descriptions.Item label="生成时间">
                  {dayjs(selectedReport.generated_at).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
                <Descriptions.Item label="总执行数">
                  {selectedReport.total_executions}
                </Descriptions.Item>
                <Descriptions.Item label="成功执行">
                  <Text style={{ color: '#3f8600' }}>{selectedReport.successful_executions}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="失败执行">
                  <Text style={{ color: '#cf1322' }}>{selectedReport.failed_executions}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="成功率">
                  <Progress percent={selectedReport.success_rate} size="small" />
                </Descriptions.Item>
                <Descriptions.Item label="平均时长">
                  {Math.floor(selectedReport.average_duration / 60)}分{selectedReport.average_duration % 60}秒
                </Descriptions.Item>
                <Descriptions.Item label="涉及设备">
                  {selectedReport.device_ids.length}个
                </Descriptions.Item>
              </Descriptions>

              <Divider />

              <div>
                <Title level={4}>报告摘要</Title>
                <Alert
                  message="测试执行概况"
                  description={`在 ${selectedReport.date_range[0]} 到 ${selectedReport.date_range[1]} 期间，共执行了 ${selectedReport.total_executions} 次测试，成功率为 ${selectedReport.success_rate}%。平均执行时间为 ${Math.floor(selectedReport.average_duration / 60)}分${selectedReport.average_duration % 60}秒。`}
                  type="info"
                  style={{ marginBottom: 16 }}
                />

                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Card size="small" title="成功率分析">
                      <Progress 
                        type="circle" 
                        percent={selectedReport.success_rate}
                        format={percent => `${percent}%`}
                      />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card size="small" title="执行分布">
                      <Statistic 
                        title="成功" 
                        value={selectedReport.successful_executions} 
                        valueStyle={{ color: '#3f8600' }}
                      />
                      <Statistic 
                        title="失败" 
                        value={selectedReport.failed_executions} 
                        valueStyle={{ color: '#cf1322' }}
                      />
                    </Card>
                  </Col>
                </Row>
              </div>
            </div>
          )}
        </Drawer>
      </motion.div>
    </div>
  );
};

export default AndroidTestReports;
