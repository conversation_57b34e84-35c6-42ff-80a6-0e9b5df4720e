import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Typography, 
  message, 
  Modal,
  Descriptions,
  Image,
  Row,
  Col,
  Statistic,
  Alert
} from 'antd';
import { 
  ReloadOutlined, 
  MobileOutlined, 
  EyeOutlined,
  CameraOutlined,
  PlayCircleOutlined,
  StopOutlined,
  SettingOutlined,
  WifiOutlined,
  UsbOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;

interface AndroidDevice {
  id: string;
  device_id: string;
  device_name?: string;
  device_model?: string;
  manufacturer?: string;
  android_version?: string;
  api_level?: number;
  screen_resolution?: string;
  screen_density?: string;
  connection_type: string;
  status: string;
  last_seen?: string;
  created_at?: string;
}

const AndroidDevicesPage: React.FC = () => {
  const [devices, setDevices] = useState<AndroidDevice[]>([]);
  const [loading, setLoading] = useState(false);
  const [scanning, setScanning] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<AndroidDevice | null>(null);
  const [deviceDetailVisible, setDeviceDetailVisible] = useState(false);
  const [screenshotVisible, setScreenshotVisible] = useState(false);
  const [screenshotData, setScreenshotData] = useState<string | null>(null);

  // 获取设备列表
  const fetchDevices = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/v1/android/devices/list');
      
      if (response.ok) {
        const data = await response.json();
        setDevices(data.data || []);
      } else {
        message.error('获取设备列表失败');
      }
    } catch (error) {
      console.error('获取设备列表失败:', error);
      message.error('获取设备列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 扫描设备
  const handleScanDevices = async () => {
    try {
      setScanning(true);
      const response = await fetch('/api/v1/android/devices/scan', {
        method: 'GET'
      });
      
      if (response.ok) {
        const data = await response.json();
        setDevices(data.data || []);
        message.success(data.message || '设备扫描完成');
      } else {
        const errorData = await response.json();
        message.error(`设备扫描失败: ${errorData.detail}`);
      }
    } catch (error) {
      console.error('扫描设备失败:', error);
      message.error('设备扫描失败');
    } finally {
      setScanning(false);
    }
  };

  // 查看设备详情
  const handleViewDevice = async (device: AndroidDevice) => {
    try {
      const response = await fetch(`/api/v1/android/devices/${device.device_id}/info`);
      
      if (response.ok) {
        const data = await response.json();
        setSelectedDevice(data.data);
        setDeviceDetailVisible(true);
      } else {
        message.error('获取设备详情失败');
      }
    } catch (error) {
      console.error('获取设备详情失败:', error);
      message.error('获取设备详情失败');
    }
  };

  // 捕获截图
  const handleCaptureScreenshot = async (deviceId: string) => {
    try {
      const response = await fetch(`/api/v1/android/devices/${deviceId}/screenshot`, {
        method: 'POST'
      });
      
      if (response.ok) {
        const data = await response.json();
        setScreenshotData(`data:image/png;base64,${data.data.screenshot_data}`);
        setScreenshotVisible(true);
        message.success('截图捕获成功');
      } else {
        message.error('截图捕获失败');
      }
    } catch (error) {
      console.error('捕获截图失败:', error);
      message.error('截图捕获失败');
    }
  };

  // 启动应用
  const handleStartApp = async (deviceId: string, packageName: string) => {
    try {
      const response = await fetch(`/api/v1/android/devices/${deviceId}/start-app`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ package_name: packageName })
      });
      
      if (response.ok) {
        message.success('应用启动成功');
      } else {
        message.error('应用启动失败');
      }
    } catch (error) {
      console.error('启动应用失败:', error);
      message.error('启动应用失败');
    }
  };

  useEffect(() => {
    fetchDevices();
  }, []);

  // 渲染连接类型
  const renderConnectionType = (type: string) => {
    const config = {
      usb: { icon: <UsbOutlined />, color: 'blue', text: 'USB' },
      wifi: { icon: <WifiOutlined />, color: 'green', text: 'WiFi' },
      emulator: { icon: <MobileOutlined />, color: 'purple', text: '模拟器' }
    };
    
    const item = config[type as keyof typeof config] || config.usb;
    return (
      <Tag icon={item.icon} color={item.color}>
        {item.text}
      </Tag>
    );
  };

  // 渲染设备状态
  const renderStatus = (status: string) => {
    const config = {
      online: { color: 'success', text: '在线' },
      offline: { color: 'default', text: '离线' },
      unauthorized: { color: 'warning', text: '未授权' },
      error: { color: 'error', text: '错误' }
    };
    
    const item = config[status as keyof typeof config] || config.offline;
    return <Tag color={item.color}>{item.text}</Tag>;
  };

  const columns: ColumnsType<AndroidDevice> = [
    {
      title: '设备信息',
      key: 'device_info',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Text strong>
            {record.device_name || record.device_model || record.device_id}
          </Text>
          {record.manufacturer && (
            <Text type="secondary">{record.manufacturer}</Text>
          )}
          <Text code style={{ fontSize: '12px' }}>{record.device_id}</Text>
        </Space>
      )
    },
    {
      title: '系统版本',
      key: 'android_version',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          {record.android_version && (
            <Tag>Android {record.android_version}</Tag>
          )}
          {record.api_level && (
            <Text type="secondary">API {record.api_level}</Text>
          )}
        </Space>
      )
    },
    {
      title: '屏幕信息',
      key: 'screen_info',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          {record.screen_resolution && (
            <Text>{record.screen_resolution}</Text>
          )}
          {record.screen_density && (
            <Text type="secondary">{record.screen_density}</Text>
          )}
        </Space>
      )
    },
    {
      title: '连接方式',
      dataIndex: 'connection_type',
      key: 'connection_type',
      render: renderConnectionType
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: renderStatus
    },
    {
      title: '最后在线',
      dataIndex: 'last_seen',
      key: 'last_seen',
      render: (lastSeen) => lastSeen ? new Date(lastSeen).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleViewDevice(record)}
          >
            详情
          </Button>
          <Button 
            size="small" 
            icon={<CameraOutlined />}
            onClick={() => handleCaptureScreenshot(record.device_id)}
            disabled={record.status !== 'online'}
          >
            截图
          </Button>
        </Space>
      )
    }
  ];

  const onlineDevices = devices.filter(device => device.status === 'online');
  const offlineDevices = devices.filter(device => device.status !== 'online');

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <MobileOutlined style={{ marginRight: '12px', color: '#1890ff' }} />
          Android设备管理
        </Title>
      </div>

      {/* 统计信息 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic 
              title="总设备数" 
              value={devices.length} 
              prefix={<MobileOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic 
              title="在线设备" 
              value={onlineDevices.length} 
              valueStyle={{ color: '#3f8600' }}
              prefix={<MobileOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic 
              title="离线设备" 
              value={offlineDevices.length} 
              valueStyle={{ color: '#cf1322' }}
              prefix={<MobileOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 设备列表 */}
      <Card 
        title="设备列表"
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchDevices}
              loading={loading}
            >
              刷新
            </Button>
            <Button 
              type="primary"
              icon={<ReloadOutlined />}
              onClick={handleScanDevices}
              loading={scanning}
            >
              扫描设备
            </Button>
          </Space>
        }
      >
        {devices.length === 0 && !loading ? (
          <Alert
            message="未发现设备"
            description="请确保Android设备已连接并开启USB调试，然后点击「扫描设备」按钮"
            type="info"
            showIcon
            style={{ margin: '40px 0' }}
          />
        ) : (
          <Table
            columns={columns}
            dataSource={devices}
            rowKey="device_id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个设备`
            }}
          />
        )}
      </Card>

      {/* 设备详情弹窗 */}
      <Modal
        title="设备详情"
        open={deviceDetailVisible}
        onCancel={() => setDeviceDetailVisible(false)}
        footer={null}
        width={800}
      >
        {selectedDevice && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="设备ID" span={2}>
              <Text code>{selectedDevice.device_id}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="设备名称">
              {selectedDevice.device_name || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="设备型号">
              {selectedDevice.device_model || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="制造商">
              {selectedDevice.manufacturer || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="Android版本">
              {selectedDevice.android_version || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="API级别">
              {selectedDevice.api_level || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="屏幕分辨率">
              {selectedDevice.screen_resolution || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="屏幕密度">
              {selectedDevice.screen_density || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="连接方式">
              {renderConnectionType(selectedDevice.connection_type)}
            </Descriptions.Item>
            <Descriptions.Item label="设备状态">
              {renderStatus(selectedDevice.status)}
            </Descriptions.Item>
            <Descriptions.Item label="最后在线">
              {selectedDevice.last_seen ? new Date(selectedDevice.last_seen).toLocaleString() : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="添加时间">
              {selectedDevice.created_at ? new Date(selectedDevice.created_at).toLocaleString() : '-'}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      {/* 截图预览弹窗 */}
      <Modal
        title="设备截图"
        open={screenshotVisible}
        onCancel={() => setScreenshotVisible(false)}
        footer={null}
        width={400}
      >
        {screenshotData && (
          <Image
            src={screenshotData}
            alt="设备截图"
            style={{ width: '100%' }}
          />
        )}
      </Modal>
    </div>
  );
};

export default AndroidDevicesPage;
