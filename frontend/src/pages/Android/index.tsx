import React from 'react';
import { Card, Row, Col, Typography, Button, Space } from 'antd';
import { 
  MobileOutlined, 
  PlayCircleOutlined, 
  CodeOutlined, 
  SettingOutlined,
  BarChartOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Paragraph } = Typography;

const AndroidPage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      title: 'Android界面分析',
      description: '智能分析Android应用界面，识别UI元素，生成测试脚本',
      icon: <MobileOutlined style={{ fontSize: '32px', color: '#1890ff' }} />,
      path: '/android/analysis',
      color: '#e6f7ff'
    },
    {
      title: '设备管理',
      description: '管理Android设备连接，监控设备状态，远程操作',
      icon: <SettingOutlined style={{ fontSize: '32px', color: '#52c41a' }} />,
      path: '/android/devices',
      color: '#f6ffed'
    },
    {
      title: '脚本管理',
      description: '创建、编辑和管理Android自动化测试脚本',
      icon: <CodeOutlined style={{ fontSize: '32px', color: '#722ed1' }} />,
      path: '/android/scripts',
      color: '#f9f0ff'
    },
    {
      title: '脚本执行',
      description: '执行Android自动化脚本，实时监控执行过程',
      icon: <PlayCircleOutlined style={{ fontSize: '32px', color: '#fa541c' }} />,
      path: '/android/execution',
      color: '#fff2e8'
    },
    {
      title: '执行历史',
      description: '查看脚本执行历史记录，分析测试结果',
      icon: <BarChartOutlined style={{ fontSize: '32px', color: '#13c2c2' }} />,
      path: '/android/history',
      color: '#e6fffb'
    },
    {
      title: '测试报告',
      description: '生成详细的测试报告，导出测试结果',
      icon: <FileTextOutlined style={{ fontSize: '32px', color: '#eb2f96' }} />,
      path: '/android/reports',
      color: '#fff0f6'
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '32px', textAlign: 'center' }}>
        <Title level={2}>
          <MobileOutlined style={{ marginRight: '12px', color: '#1890ff' }} />
          Android自动化测试平台
        </Title>
        <Paragraph style={{ fontSize: '16px', color: '#666' }}>
          基于AI的Android应用界面分析与自动化测试解决方案
        </Paragraph>
      </div>

      <Row gutter={[24, 24]}>
        {features.map((feature, index) => (
          <Col xs={24} sm={12} lg={8} key={index}>
            <Card
              hoverable
              style={{ 
                height: '200px',
                backgroundColor: feature.color,
                border: 'none',
                borderRadius: '12px'
              }}
              bodyStyle={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column',
                justifyContent: 'space-between'
              }}
              onClick={() => navigate(feature.path)}
            >
              <div>
                <div style={{ marginBottom: '16px' }}>
                  {feature.icon}
                </div>
                <Title level={4} style={{ marginBottom: '8px' }}>
                  {feature.title}
                </Title>
                <Paragraph style={{ color: '#666', marginBottom: '16px' }}>
                  {feature.description}
                </Paragraph>
              </div>
              <Button type="primary" block>
                进入模块
              </Button>
            </Card>
          </Col>
        ))}
      </Row>

      <div style={{ marginTop: '48px' }}>
        <Card style={{ borderRadius: '12px' }}>
          <Title level={3}>快速开始</Title>
          <Row gutter={[24, 24]}>
            <Col xs={24} md={8}>
              <Card size="small" style={{ backgroundColor: '#f0f9ff' }}>
                <Title level={5}>1. 连接设备</Title>
                <Paragraph>
                  通过USB或WiFi连接Android设备，确保ADB调试已开启
                </Paragraph>
                <Button 
                  type="link" 
                  onClick={() => navigate('/android/devices')}
                >
                  管理设备 →
                </Button>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card size="small" style={{ backgroundColor: '#f0fff0' }}>
                <Title level={5}>2. 分析界面</Title>
                <Paragraph>
                  上传应用截图或实时捕获，AI智能分析界面元素
                </Paragraph>
                <Button 
                  type="link" 
                  onClick={() => navigate('/android/analysis')}
                >
                  开始分析 →
                </Button>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card size="small" style={{ backgroundColor: '#fff9f0' }}>
                <Title level={5}>3. 执行测试</Title>
                <Paragraph>
                  生成自动化脚本，在真实设备上执行测试
                </Paragraph>
                <Button 
                  type="link" 
                  onClick={() => navigate('/android/execution')}
                >
                  执行测试 →
                </Button>
              </Card>
            </Col>
          </Row>
        </Card>
      </div>

      <div style={{ marginTop: '32px' }}>
        <Card style={{ borderRadius: '12px' }}>
          <Title level={3}>平台特性</Title>
          <Row gutter={[24, 24]}>
            <Col xs={24} md={12}>
              <Space direction="vertical" size="middle">
                <div>
                  <Title level={5}>🤖 AI智能分析</Title>
                  <Paragraph>
                    基于深度学习的UI元素识别，自动生成测试脚本
                  </Paragraph>
                </div>
                <div>
                  <Title level={5}>📱 多设备支持</Title>
                  <Paragraph>
                    支持真机和模拟器，多设备并行测试
                  </Paragraph>
                </div>
                <div>
                  <Title level={5}>🔄 实时监控</Title>
                  <Paragraph>
                    实时查看测试执行过程，支持截图和录屏
                  </Paragraph>
                </div>
              </Space>
            </Col>
            <Col xs={24} md={12}>
              <Space direction="vertical" size="middle">
                <div>
                  <Title level={5}>📊 详细报告</Title>
                  <Paragraph>
                    生成详细的测试报告，支持多种格式导出
                  </Paragraph>
                </div>
                <div>
                  <Title level={5}>🔧 多框架支持</Title>
                  <Paragraph>
                    支持Appium、UIAutomator2等主流测试框架
                  </Paragraph>
                </div>
                <div>
                  <Title level={5}>☁️ 云端部署</Title>
                  <Paragraph>
                    支持云端设备，随时随地进行测试
                  </Paragraph>
                </div>
              </Space>
            </Col>
          </Row>
        </Card>
      </div>
    </div>
  );
};

export default AndroidPage;
