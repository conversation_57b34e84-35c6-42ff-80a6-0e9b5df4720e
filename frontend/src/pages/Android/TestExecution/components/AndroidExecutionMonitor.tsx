import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Progress,
  Typography,
  Space,
  Alert,
  Timeline,
  Tag,
  Button,
  Row,
  Col,
  Statistic,
  Image,
  Collapse,
  List
} from 'antd';
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  CameraOutlined,
  FileTextOutlined,
  BugOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Panel } = Collapse;

interface AndroidExecutionMonitorProps {
  executionId: string;
  onStatusChange?: (status: string, progress: number) => void;
}

interface ExecutionMessage {
  type: string;
  message: string;
  progress?: number;
  timestamp?: string;
  data?: any;
}

const AndroidExecutionMonitor: React.FC<AndroidExecutionMonitorProps> = ({
  executionId,
  onStatusChange
}) => {
  const [status, setStatus] = useState<'pending' | 'running' | 'completed' | 'failed' | 'cancelled'>('pending');
  const [progress, setProgress] = useState(0);
  const [currentMessage, setCurrentMessage] = useState('准备开始执行...');
  const [executionLogs, setExecutionLogs] = useState<ExecutionMessage[]>([]);
  const [screenshots, setScreenshots] = useState<string[]>([]);
  const [executionResult, setExecutionResult] = useState<any>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [endTime, setEndTime] = useState<Date | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  useEffect(() => {
    if (!executionId) return;

    // 建立SSE连接
    const eventSource = new EventSource(`/api/v1/android/execution/stream/${executionId}`);
    eventSourceRef.current = eventSource;

    eventSource.onopen = () => {
      console.log('执行监控连接已建立');
      setStartTime(new Date());
    };

    eventSource.onmessage = (event) => {
      try {
        const data: ExecutionMessage = JSON.parse(event.data);
        
        // 添加时间戳
        const messageWithTimestamp = {
          ...data,
          timestamp: new Date().toISOString()
        };
        
        setExecutionLogs(prev => [...prev, messageWithTimestamp]);

        switch (data.type) {
          case 'progress':
            setProgress(data.progress || 0);
            setCurrentMessage(data.message);
            setStatus('running');
            break;
            
          case 'screenshot':
            if (data.data?.screenshot) {
              setScreenshots(prev => [...prev, data.data.screenshot]);
            }
            break;
            
          case 'complete':
            setStatus('completed');
            setProgress(100);
            setCurrentMessage(data.message);
            setEndTime(new Date());
            if (data.data) {
              setExecutionResult(data.data);
            }
            break;
            
          case 'error':
          case 'failed':
            setStatus('failed');
            setCurrentMessage(data.message);
            setEndTime(new Date());
            break;
            
          case 'cancelled':
            setStatus('cancelled');
            setCurrentMessage(data.message);
            setEndTime(new Date());
            break;
        }

        // 通知父组件状态变化
        if (onStatusChange) {
          onStatusChange(status, progress);
        }
      } catch (err) {
        console.error('解析执行消息失败:', err);
      }
    };

    eventSource.onerror = (event) => {
      console.error('执行监控连接错误:', event);
      setStatus('failed');
      setCurrentMessage('连接中断');
      eventSource.close();
    };

    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [executionId, onStatusChange, status, progress]);

  // 渲染状态图标
  const renderStatusIcon = () => {
    switch (status) {
      case 'running':
        return <PlayCircleOutlined style={{ color: '#1890ff' }} />;
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'cancelled':
        return <CloseCircleOutlined style={{ color: '#d9d9d9' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  // 渲染状态标签
  const renderStatusTag = () => {
    const statusConfig = {
      pending: { color: 'default', text: '等待中' },
      running: { color: 'processing', text: '执行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '执行失败' },
      cancelled: { color: 'default', text: '已取消' }
    };
    
    const config = statusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 计算执行时间
  const getExecutionDuration = () => {
    if (!startTime) return '0秒';
    const end = endTime || new Date();
    const duration = Math.floor((end.getTime() - startTime.getTime()) / 1000);
    return `${duration}秒`;
  };

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* 执行状态概览 */}
        <Col xs={24} lg={16}>
          <Card title="执行状态" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                {renderStatusIcon()}
                <Text strong style={{ fontSize: '16px' }}>{currentMessage}</Text>
                {renderStatusTag()}
              </div>
              
              <Progress 
                percent={progress} 
                status={status === 'failed' ? 'exception' : undefined}
                strokeColor={status === 'completed' ? '#52c41a' : undefined}
              />

              {status === 'running' && (
                <Alert
                  message="执行进行中"
                  description="请耐心等待测试执行完成，您可以在下方查看实时日志"
                  type="info"
                  showIcon
                />
              )}

              {status === 'completed' && (
                <Alert
                  message="执行完成"
                  description="测试执行已成功完成，您可以查看执行结果和截图"
                  type="success"
                  showIcon
                />
              )}

              {status === 'failed' && (
                <Alert
                  message="执行失败"
                  description="测试执行过程中出现错误，请查看错误日志"
                  type="error"
                  showIcon
                />
              )}
            </Space>
          </Card>
        </Col>

        {/* 执行统计 */}
        <Col xs={24} lg={8}>
          <Card title="执行统计" size="small">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic 
                  title="执行进度" 
                  value={progress} 
                  suffix="%" 
                  valueStyle={{ color: progress === 100 ? '#3f8600' : '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="执行时间" 
                  value={getExecutionDuration()} 
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="截图数量" 
                  value={screenshots.length} 
                  prefix={<CameraOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="日志条数" 
                  value={executionLogs.length} 
                  prefix={<FileTextOutlined />}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
        {/* 执行日志 */}
        <Col xs={24} lg={12}>
          <Card title="执行日志" size="small">
            <div style={{ maxHeight: '400px', overflow: 'auto' }}>
              <Timeline>
                {executionLogs.map((log, index) => (
                  <Timeline.Item
                    key={index}
                    color={
                      log.type === 'error' ? 'red' :
                      log.type === 'complete' ? 'green' :
                      log.type === 'progress' ? 'blue' : 'gray'
                    }
                  >
                    <div>
                      <Text strong>{log.message}</Text>
                      {log.progress && (
                        <Tag size="small" style={{ marginLeft: 8 }}>
                          {log.progress}%
                        </Tag>
                      )}
                      <br />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {log.timestamp ? new Date(log.timestamp).toLocaleTimeString() : ''}
                      </Text>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </div>
          </Card>
        </Col>

        {/* 执行截图 */}
        <Col xs={24} lg={12}>
          <Card title="执行截图" size="small">
            {screenshots.length > 0 ? (
              <div style={{ maxHeight: '400px', overflow: 'auto' }}>
                <Row gutter={[8, 8]}>
                  {screenshots.map((screenshot, index) => (
                    <Col xs={12} sm={8} key={index}>
                      <Image
                        src={`data:image/png;base64,${screenshot}`}
                        alt={`截图 ${index + 1}`}
                        style={{ width: '100%' }}
                        preview={{
                          mask: <EyeOutlined />
                        }}
                      />
                    </Col>
                  ))}
                </Row>
              </div>
            ) : (
              <Alert
                message="暂无截图"
                description="执行过程中的截图将在这里显示"
                type="info"
                showIcon
              />
            )}
          </Card>
        </Col>
      </Row>

      {/* 执行结果 */}
      {executionResult && (
        <Card title="执行结果" style={{ marginTop: 24 }} size="small">
          <Collapse>
            <Panel header="详细结果" key="result">
              <pre style={{ 
                backgroundColor: '#f5f5f5', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto',
                maxHeight: '300px'
              }}>
                {JSON.stringify(executionResult, null, 2)}
              </pre>
            </Panel>
          </Collapse>
        </Card>
      )}
    </div>
  );
};

export default AndroidExecutionMonitor;
