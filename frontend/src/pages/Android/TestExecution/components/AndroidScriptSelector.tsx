import React, { useState } from 'react';
import { Select, Button, Space, Tag, Tooltip, Modal, Typography } from 'antd';
import { 
  CodeOutlined, 
  EyeOutlined, 
  ReloadOutlined,
  FileTextOutlined,
  CalendarOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { Text } = Typography;

interface AndroidScript {
  id: string;
  script_name: string;
  script_type: string;
  script_language: string;
  script_content: string;
  test_description: string;
  created_at?: string;
  updated_at?: string;
}

interface AndroidScriptSelectorProps {
  value?: string;
  onChange?: (scriptId: string | null) => void;
  scripts: AndroidScript[];
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  style?: React.CSSProperties;
  onRefresh?: () => void;
}

const AndroidScriptSelector: React.FC<AndroidScriptSelectorProps> = ({
  value,
  onChange,
  scripts,
  placeholder = "选择Android脚本",
  allowClear = true,
  disabled = false,
  style,
  onRefresh
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [selectedScript, setSelectedScript] = useState<AndroidScript | null>(null);

  // 预览脚本
  const handlePreviewScript = (script: AndroidScript) => {
    setSelectedScript(script);
    setPreviewVisible(true);
  };

  // 渲染脚本类型标签
  const renderScriptTypeTag = (type: string) => {
    const typeConfig = {
      appium: { color: 'blue', text: 'Appium' },
      uiautomator2: { color: 'green', text: 'UIAutomator2' },
      midscene: { color: 'purple', text: 'Midscene' },
      custom: { color: 'orange', text: '自定义' }
    };
    
    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.custom;
    return <Tag color={config.color} size="small">{config.text}</Tag>;
  };

  // 渲染脚本语言标签
  const renderLanguageTag = (language: string) => {
    const languageConfig = {
      python: { color: 'cyan', text: 'Python' },
      javascript: { color: 'gold', text: 'JavaScript' },
      java: { color: 'red', text: 'Java' },
      typescript: { color: 'geekblue', text: 'TypeScript'
      }
    };
    
    const config = languageConfig[language as keyof typeof languageConfig] || { color: 'default', text: language };
    return <Tag color={config.color} size="small">{config.text}</Tag>;
  };

  // 渲染脚本选项
  const renderScriptOption = (script: AndroidScript) => {
    return (
      <Option key={script.id} value={script.id}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            <CodeOutlined />
            <span>
              <strong>{script.script_name}</strong>
              <br />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {script.test_description}
              </Text>
            </span>
          </Space>
          <Space>
            {renderScriptTypeTag(script.script_type)}
            {renderLanguageTag(script.script_language)}
            <Tooltip title="预览脚本">
              <Button
                size="small"
                type="text"
                icon={<EyeOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handlePreviewScript(script);
                }}
              />
            </Tooltip>
          </Space>
        </div>
        {script.created_at && (
          <div style={{ fontSize: '11px', color: '#999', marginTop: '4px' }}>
            <CalendarOutlined style={{ marginRight: '4px' }} />
            创建时间: {new Date(script.created_at).toLocaleString()}
          </div>
        )}
      </Option>
    );
  };

  return (
    <>
      <Space.Compact style={{ width: '100%', ...style }}>
        <Select
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          allowClear={allowClear}
          disabled={disabled}
          style={{ flex: 1 }}
          optionLabelProp="label"
          showSearch
          filterOption={(input, option) => {
            const script = scripts.find(s => s.id === option?.value);
            if (!script) return false;
            
            const searchText = input.toLowerCase();
            return (
              script.script_name.toLowerCase().includes(searchText) ||
              script.test_description.toLowerCase().includes(searchText) ||
              script.script_type.toLowerCase().includes(searchText) ||
              script.script_language.toLowerCase().includes(searchText)
            );
          }}
          notFoundContent={
            scripts.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                <FileTextOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                <div>暂无脚本</div>
                <div style={{ fontSize: '12px' }}>请先创建或生成Android测试脚本</div>
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                <div>未找到匹配的脚本</div>
              </div>
            )
          }
        >
          {scripts.map(renderScriptOption)}
        </Select>
        
        {onRefresh && (
          <Tooltip title="刷新脚本列表">
            <Button 
              icon={<ReloadOutlined />}
              onClick={onRefresh}
              disabled={disabled}
            />
          </Tooltip>
        )}
      </Space.Compact>

      {/* 脚本预览弹窗 */}
      <Modal
        title={
          <Space>
            <CodeOutlined />
            脚本预览: {selectedScript?.script_name}
          </Space>
        }
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedScript && (
          <div>
            <Space style={{ marginBottom: 16 }}>
              {renderScriptTypeTag(selectedScript.script_type)}
              {renderLanguageTag(selectedScript.script_language)}
            </Space>
            
            <div style={{ marginBottom: 16 }}>
              <Text strong>测试描述: </Text>
              <Text>{selectedScript.test_description}</Text>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <Text strong>脚本内容:</Text>
            </div>
            
            <pre style={{ 
              backgroundColor: '#f5f5f5', 
              padding: '12px', 
              borderRadius: '4px',
              fontSize: '12px',
              overflow: 'auto',
              maxHeight: '400px',
              border: '1px solid #d9d9d9'
            }}>
              {selectedScript.script_content}
            </pre>
            
            {selectedScript.created_at && (
              <div style={{ marginTop: 16, fontSize: '12px', color: '#999' }}>
                <CalendarOutlined style={{ marginRight: '4px' }} />
                创建时间: {new Date(selectedScript.created_at).toLocaleString()}
                {selectedScript.updated_at && selectedScript.updated_at !== selectedScript.created_at && (
                  <>
                    <span style={{ margin: '0 8px' }}>|</span>
                    更新时间: {new Date(selectedScript.updated_at).toLocaleString()}
                  </>
                )}
              </div>
            )}
          </div>
        )}
      </Modal>
    </>
  );
};

export default AndroidScriptSelector;
