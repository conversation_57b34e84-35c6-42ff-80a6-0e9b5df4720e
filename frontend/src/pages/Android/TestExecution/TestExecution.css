/* Android测试执行页面样式 */
.android-test-execution {
  padding: 24px;
}

.android-test-execution .execution-config {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.android-test-execution .execution-status {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  padding: 20px;
}

.android-test-execution .execution-monitor {
  min-height: 500px;
}

.android-test-execution .execution-logs {
  max-height: 400px;
  overflow-y: auto;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
}

.android-test-execution .screenshot-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 12px;
}

.android-test-execution .screenshot-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.android-test-execution .screenshot-item:hover {
  transform: scale(1.05);
}

.android-test-execution .progress-ring {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
}

.android-test-execution .status-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.android-test-execution .status-badge.running {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.android-test-execution .status-badge.completed {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.android-test-execution .status-badge.failed {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}
