import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Tabs,
  message,
  Badge,
  Tooltip,
  Select,
  Input,
  Table,
  Tag,
  Progress,
  Alert,
  Modal,
  Image
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  <PERSON>Outlined,
  SettingOutlined,
  <PERSON>Outlined,
  CodeOutlined,
  Bar<PERSON><PERSON>Outlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import AndroidDeviceSelector from '../components/AndroidDeviceSelector';
import AndroidExecutionMonitor from './components/AndroidExecutionMonitor';
import AndroidScriptSelector from './components/AndroidScriptSelector';
import './TestExecution.css';
import { 
  getAndroidScriptList, 
  startAndroidExecution, 
  stopAndroidExecution,
  getAndroidExecutionHistory 
} from '../../../services/androidApi';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Search } = Input;

interface ExecutionSession {
  execution_id: string;
  script_id: string;
  device_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  start_time: string;
  script_name?: string;
}

const AndroidTestExecution: React.FC = () => {
  const [activeTab, setActiveTab] = useState('scripts');
  const [selectedDevice, setSelectedDevice] = useState<string | null>(null);
  const [selectedScript, setSelectedScript] = useState<string | null>(null);
  const [currentExecution, setCurrentExecution] = useState<ExecutionSession | null>(null);
  const [executionHistory, setExecutionHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [scripts, setScripts] = useState<any[]>([]);

  // 获取脚本列表
  const fetchScripts = async () => {
    try {
      const result = await getAndroidScriptList();
      setScripts(result.data);
    } catch (error) {
      message.error('获取脚本列表失败');
    }
  };

  // 获取执行历史
  const fetchExecutionHistory = async () => {
    try {
      const result = await getAndroidExecutionHistory();
      setExecutionHistory(result.data);
    } catch (error) {
      message.error('获取执行历史失败');
    }
  };

  useEffect(() => {
    fetchScripts();
    fetchExecutionHistory();
  }, []);

  // 开始执行
  const handleStartExecution = async () => {
    if (!selectedDevice || !selectedScript) {
      message.error('请选择设备和脚本');
      return;
    }

    try {
      setLoading(true);
      const executionRequest = {
        session_id: `android_exec_${Date.now()}`,
        script_id: selectedScript,
        device_id: selectedDevice,
        execution_config: {
          timeout_seconds: 300,
          capture_screenshots: true,
          record_video: false
        },
        capture_screenshots: true,
        record_video: false,
        timeout_seconds: 300
      };

      const result = await startAndroidExecution(executionRequest);
      
      setCurrentExecution({
        execution_id: result.execution_id,
        script_id: selectedScript,
        device_id: selectedDevice,
        status: 'pending',
        progress: 0,
        start_time: new Date().toISOString(),
        script_name: scripts.find(s => s.id === selectedScript)?.script_name
      });

      setActiveTab('monitor');
      message.success('测试执行已启动');
    } catch (error) {
      message.error('启动执行失败');
    } finally {
      setLoading(false);
    }
  };

  // 停止执行
  const handleStopExecution = async () => {
    if (!currentExecution) return;

    try {
      await stopAndroidExecution(currentExecution.execution_id);
      setCurrentExecution({
        ...currentExecution,
        status: 'cancelled'
      });
      message.success('执行已停止');
    } catch (error) {
      message.error('停止执行失败');
    }
  };

  // 执行历史表格列
  const historyColumns = [
    {
      title: '执行名称',
      dataIndex: 'execution_name',
      key: 'execution_name',
    },
    {
      title: '脚本',
      dataIndex: 'script_id',
      key: 'script_id',
      render: (scriptId: string) => {
        const script = scripts.find(s => s.id === scriptId);
        return script?.script_name || scriptId;
      }
    },
    {
      title: '设备',
      dataIndex: 'device_id',
      key: 'device_id',
    },
    {
      title: '状态',
      dataIndex: 'execution_status',
      key: 'execution_status',
      render: (status: string) => {
        const statusConfig = {
          completed: { color: 'success', text: '完成' },
          failed: { color: 'error', text: '失败' },
          running: { color: 'processing', text: '运行中' },
          cancelled: { color: 'default', text: '已取消' }
        };
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.failed;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      render: (time: string) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '持续时间',
      dataIndex: 'duration_seconds',
      key: 'duration_seconds',
      render: (duration: number) => duration ? `${duration}秒` : '-'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: any) => (
        <Space>
          <Button 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => {
              // 查看执行详情
              Modal.info({
                title: '执行详情',
                content: (
                  <div>
                    <p>执行ID: {record.id}</p>
                    <p>状态: {record.execution_status}</p>
                    <p>错误信息: {record.error_message || '无'}</p>
                  </div>
                ),
                width: 600
              });
            }}
          >
            详情
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div style={{ marginBottom: '24px' }}>
          <Title level={2}>
            <PlayCircleOutlined style={{ marginRight: '12px', color: '#fa541c' }} />
            Android测试执行
          </Title>
          <Text type="secondary">
            选择脚本和设备，执行Android自动化测试
          </Text>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <CodeOutlined />
                脚本执行
              </span>
            } 
            key="scripts"
          >
            <Row gutter={[24, 24]}>
              <Col xs={24} lg={12}>
                <Card title="执行配置" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text strong>选择设备:</Text>
                      <AndroidDeviceSelector
                        value={selectedDevice}
                        onChange={setSelectedDevice}
                        placeholder="选择要执行测试的设备"
                        style={{ marginTop: 8 }}
                      />
                    </div>

                    <div>
                      <Text strong>选择脚本:</Text>
                      <AndroidScriptSelector
                        value={selectedScript}
                        onChange={setSelectedScript}
                        scripts={scripts}
                        placeholder="选择要执行的脚本"
                        style={{ marginTop: 8 }}
                      />
                    </div>

                    <Button
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      onClick={handleStartExecution}
                      loading={loading}
                      disabled={!selectedDevice || !selectedScript}
                      size="large"
                      block
                    >
                      开始执行
                    </Button>
                  </Space>
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card title="当前执行状态" size="small">
                  {currentExecution ? (
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text strong>执行ID:</Text> {currentExecution.execution_id}
                      </div>
                      <div>
                        <Text strong>脚本:</Text> {currentExecution.script_name}
                      </div>
                      <div>
                        <Text strong>设备:</Text> {currentExecution.device_id}
                      </div>
                      <div>
                        <Text strong>状态:</Text> 
                        <Tag color={currentExecution.status === 'running' ? 'processing' : 'default'}>
                          {currentExecution.status}
                        </Tag>
                      </div>
                      <Progress percent={currentExecution.progress} />
                      
                      {currentExecution.status === 'running' && (
                        <Button
                          danger
                          icon={<StopOutlined />}
                          onClick={handleStopExecution}
                        >
                          停止执行
                        </Button>
                      )}
                    </Space>
                  ) : (
                    <Alert
                      message="暂无执行任务"
                      description="请选择脚本和设备后开始执行"
                      type="info"
                      showIcon
                    />
                  )}
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane 
            tab={
              <span>
                <BarChartOutlined />
                执行监控
                {currentExecution && (
                  <Badge 
                    status={currentExecution.status === 'running' ? 'processing' : 'default'} 
                    style={{ marginLeft: 8 }}
                  />
                )}
              </span>
            } 
            key="monitor"
          >
            {currentExecution ? (
              <AndroidExecutionMonitor 
                executionId={currentExecution.execution_id}
                onStatusChange={(status, progress) => {
                  setCurrentExecution({
                    ...currentExecution,
                    status,
                    progress
                  });
                }}
              />
            ) : (
              <Card>
                <Alert
                  message="暂无执行任务"
                  description="请先在脚本执行页面启动一个测试任务"
                  type="info"
                  showIcon
                  action={
                    <Button 
                      size="small" 
                      type="primary"
                      onClick={() => setActiveTab('scripts')}
                    >
                      去执行
                    </Button>
                  }
                />
              </Card>
            )}
          </TabPane>

          <TabPane 
            tab={
              <span>
                <ClockCircleOutlined />
                执行历史
              </span>
            } 
            key="history"
          >
            <Card 
              title="执行历史记录"
              extra={
                <Space>
                  <Button 
                    icon={<ReloadOutlined />}
                    onClick={fetchExecutionHistory}
                  >
                    刷新
                  </Button>
                </Space>
              }
            >
              <Table
                columns={historyColumns}
                dataSource={executionHistory}
                rowKey="id"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`
                }}
              />
            </Card>
          </TabPane>
        </Tabs>
      </motion.div>
    </div>
  );
};

export default AndroidTestExecution;
