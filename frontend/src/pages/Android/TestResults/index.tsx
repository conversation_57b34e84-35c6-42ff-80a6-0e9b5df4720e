import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Progress,
  Tabs,
  Modal,
  Image,
  Input,
  Select,
  DatePicker,
  message,
  Drawer,
  Descriptions,
  Alert,
  Badge,
  Divider,
  Timeline,
  Empty
} from 'antd';
import {
  EyeOutlined,
  DownloadOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  Bar<PERSON>hartOutlined,
  FileTextOutlined,
  SearchOutlined,
  FilterOutlined,
  CalendarOutlined,
  MobileOutlined,
  PlayCircleOutlined,
  CameraOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import dayjs from 'dayjs';
import { getAndroidExecutionHistory } from '../../../services/androidApi';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Search } = Input;
const { RangePicker } = DatePicker;

interface ExecutionResult {
  id: string;
  session_id: string;
  script_id: string;
  device_id: string;
  execution_name: string;
  execution_status: string;
  start_time: string;
  end_time?: string;
  duration_seconds?: number;
  execution_result?: any;
  test_results?: any;
  execution_logs?: string;
  screenshots?: string[];
  video_path?: string;
  error_message?: string;
  created_at: string;
}

const AndroidTestResults: React.FC = () => {
  const [results, setResults] = useState<ExecutionResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedResult, setSelectedResult] = useState<ExecutionResult | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  // 获取执行结果
  const fetchResults = async () => {
    try {
      setLoading(true);
      const response = await getAndroidExecutionHistory();
      setResults(response.data);
    } catch (error) {
      message.error('获取测试结果失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchResults();
  }, []);

  // 查看详细结果
  const handleViewDetail = async (record: ExecutionResult) => {
    try {
      setSelectedResult(record);
      setDetailVisible(true);
    } catch (error) {
      message.error('获取详细结果失败');
    }
  };

  // 下载结果
  const handleDownloadResult = (record: ExecutionResult) => {
    const data = {
      execution_info: {
        id: record.id,
        name: record.execution_name,
        status: record.execution_status,
        start_time: record.start_time,
        end_time: record.end_time,
        duration: record.duration_seconds
      },
      execution_result: record.execution_result,
      test_results: record.test_results,
      logs: record.execution_logs
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `android_test_result_${record.id}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 渲染状态标签
  const renderStatus = (status: string) => {
    const statusConfig = {
      completed: { color: 'success', text: '完成', icon: <CheckCircleOutlined /> },
      failed: { color: 'error', text: '失败', icon: <CloseCircleOutlined /> },
      running: { color: 'processing', text: '运行中', icon: <ClockCircleOutlined /> },
      cancelled: { color: 'default', text: '已取消', icon: <CloseCircleOutlined /> },
      pending: { color: 'warning', text: '等待中', icon: <ClockCircleOutlined /> }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.failed;
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 计算统计数据
  const getStatistics = () => {
    const total = results.length;
    const completed = results.filter(r => r.execution_status === 'completed').length;
    const failed = results.filter(r => r.execution_status === 'failed').length;
    const running = results.filter(r => r.execution_status === 'running').length;
    const successRate = total > 0 ? Math.round((completed / total) * 100) : 0;

    return { total, completed, failed, running, successRate };
  };

  // 过滤结果
  const filteredResults = results.filter(result => {
    const matchesSearch = !searchText || 
      result.execution_name.toLowerCase().includes(searchText.toLowerCase()) ||
      result.device_id.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesStatus = !statusFilter || result.execution_status === statusFilter;
    
    const matchesDate = !dateRange || (
      dayjs(result.start_time).isAfter(dateRange[0]) &&
      dayjs(result.start_time).isBefore(dateRange[1])
    );

    return matchesSearch && matchesStatus && matchesDate;
  });

  const statistics = getStatistics();

  // 表格列定义
  const columns = [
    {
      title: '执行名称',
      dataIndex: 'execution_name',
      key: 'execution_name',
      render: (text: string, record: ExecutionResult) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ID: {record.id}
          </Text>
        </div>
      )
    },
    {
      title: '设备',
      dataIndex: 'device_id',
      key: 'device_id',
      render: (deviceId: string) => (
        <Tag icon={<MobileOutlined />}>{deviceId}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'execution_status',
      key: 'execution_status',
      render: renderStatus
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      render: (time: string) => (
        <div>
          <div>{dayjs(time).format('YYYY-MM-DD')}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {dayjs(time).format('HH:mm:ss')}
          </Text>
        </div>
      )
    },
    {
      title: '持续时间',
      dataIndex: 'duration_seconds',
      key: 'duration_seconds',
      render: (duration: number) => {
        if (!duration) return '-';
        const minutes = Math.floor(duration / 60);
        const seconds = duration % 60;
        return `${minutes}分${seconds}秒`;
      }
    },
    {
      title: '截图',
      key: 'screenshots',
      render: (_, record: ExecutionResult) => (
        <Badge count={record.screenshots?.length || 0}>
          <CameraOutlined style={{ fontSize: '16px' }} />
        </Badge>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: ExecutionResult) => (
        <Space>
          <Button 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button 
            size="small" 
            icon={<DownloadOutlined />}
            onClick={() => handleDownloadResult(record)}
          >
            下载
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div style={{ marginBottom: '24px' }}>
          <Title level={2}>
            <BarChartOutlined style={{ marginRight: '12px', color: '#13c2c2' }} />
            Android测试结果
          </Title>
          <Text type="secondary">
            查看和分析Android自动化测试的执行结果
          </Text>
        </div>

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic 
                title="总执行数" 
                value={statistics.total} 
                prefix={<PlayCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic 
                title="成功执行" 
                value={statistics.completed} 
                valueStyle={{ color: '#3f8600' }}
                prefix={<CheckCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic 
                title="失败执行" 
                value={statistics.failed} 
                valueStyle={{ color: '#cf1322' }}
                prefix={<CloseCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic 
                title="成功率" 
                value={statistics.successRate} 
                suffix="%" 
                valueStyle={{ color: statistics.successRate >= 80 ? '#3f8600' : '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 过滤器 */}
        <Card style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8}>
              <Search
                placeholder="搜索执行名称或设备ID"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
            </Col>
            <Col xs={24} sm={6}>
              <Select
                placeholder="状态筛选"
                value={statusFilter}
                onChange={setStatusFilter}
                allowClear
                style={{ width: '100%' }}
              >
                <Select.Option value="completed">已完成</Select.Option>
                <Select.Option value="failed">失败</Select.Option>
                <Select.Option value="running">运行中</Select.Option>
                <Select.Option value="cancelled">已取消</Select.Option>
              </Select>
            </Col>
            <Col xs={24} sm={8}>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
              />
            </Col>
            <Col xs={24} sm={2}>
              <Button 
                icon={<ReloadOutlined />}
                onClick={fetchResults}
                loading={loading}
              >
                刷新
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 结果表格 */}
        <Card title="执行结果列表">
          <Table
            columns={columns}
            dataSource={filteredResults}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
          />
        </Card>

        {/* 详情抽屉 */}
        <Drawer
          title="执行详情"
          placement="right"
          width={800}
          open={detailVisible}
          onClose={() => setDetailVisible(false)}
        >
          {selectedResult && (
            <div>
              <Descriptions column={2} bordered style={{ marginBottom: '24px' }}>
                <Descriptions.Item label="执行名称" span={2}>
                  {selectedResult.execution_name}
                </Descriptions.Item>
                <Descriptions.Item label="执行ID">
                  {selectedResult.id}
                </Descriptions.Item>
                <Descriptions.Item label="会话ID">
                  {selectedResult.session_id}
                </Descriptions.Item>
                <Descriptions.Item label="设备ID">
                  {selectedResult.device_id}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  {renderStatus(selectedResult.execution_status)}
                </Descriptions.Item>
                <Descriptions.Item label="开始时间">
                  {dayjs(selectedResult.start_time).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
                <Descriptions.Item label="结束时间">
                  {selectedResult.end_time ? 
                    dayjs(selectedResult.end_time).format('YYYY-MM-DD HH:mm:ss') : 
                    '-'
                  }
                </Descriptions.Item>
                <Descriptions.Item label="持续时间">
                  {selectedResult.duration_seconds ? 
                    `${Math.floor(selectedResult.duration_seconds / 60)}分${selectedResult.duration_seconds % 60}秒` : 
                    '-'
                  }
                </Descriptions.Item>
              </Descriptions>

              {selectedResult.error_message && (
                <Alert
                  message="错误信息"
                  description={selectedResult.error_message}
                  type="error"
                  style={{ marginBottom: '24px' }}
                />
              )}

              <Tabs defaultActiveKey="screenshots">
                <TabPane tab="执行截图" key="screenshots">
                  {selectedResult.screenshots && selectedResult.screenshots.length > 0 ? (
                    <Row gutter={[8, 8]}>
                      {selectedResult.screenshots.map((screenshot, index) => (
                        <Col xs={12} sm={8} md={6} key={index}>
                          <Image
                            src={`data:image/png;base64,${screenshot}`}
                            alt={`截图 ${index + 1}`}
                            style={{ width: '100%' }}
                          />
                        </Col>
                      ))}
                    </Row>
                  ) : (
                    <Empty description="暂无截图" />
                  )}
                </TabPane>

                <TabPane tab="执行日志" key="logs">
                  {selectedResult.execution_logs ? (
                    <pre style={{ 
                      backgroundColor: '#f5f5f5', 
                      padding: '12px', 
                      borderRadius: '4px',
                      fontSize: '12px',
                      overflow: 'auto',
                      maxHeight: '400px'
                    }}>
                      {selectedResult.execution_logs}
                    </pre>
                  ) : (
                    <Empty description="暂无日志" />
                  )}
                </TabPane>

                <TabPane tab="测试结果" key="results">
                  {selectedResult.test_results ? (
                    <pre style={{ 
                      backgroundColor: '#f5f5f5', 
                      padding: '12px', 
                      borderRadius: '4px',
                      fontSize: '12px',
                      overflow: 'auto',
                      maxHeight: '400px'
                    }}>
                      {JSON.stringify(selectedResult.test_results, null, 2)}
                    </pre>
                  ) : (
                    <Empty description="暂无测试结果" />
                  )}
                </TabPane>
              </Tabs>
            </div>
          )}
        </Drawer>
      </motion.div>
    </div>
  );
};

export default AndroidTestResults;
