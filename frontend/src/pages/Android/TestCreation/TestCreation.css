/* Android测试创建页面样式 */
.android-test-creation {
  padding: 24px;
}

.android-test-creation .ant-steps {
  margin-bottom: 32px;
}

.android-test-creation .step-content {
  min-height: 400px;
}

.android-test-creation .screenshot-preview {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.android-test-creation .device-selector {
  margin-bottom: 16px;
}

.android-test-creation .upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background: #fafafa;
  transition: border-color 0.3s;
}

.android-test-creation .upload-area:hover {
  border-color: #1890ff;
}

.android-test-creation .completion-icon {
  font-size: 64px;
  color: #52c41a;
  margin-bottom: 16px;
}

.android-test-creation .step-actions {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}
