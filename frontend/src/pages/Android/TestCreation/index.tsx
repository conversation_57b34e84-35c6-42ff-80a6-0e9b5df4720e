import React, { useState, useEffect } from 'react';
import {
  Card,
  Steps,
  Button,
  Form,
  Input,
  Select,
  Upload,
  Space,
  Typography,
  Row,
  Col,
  Alert,
  Divider,
  Tag,
  message,
  Radio,
  Checkbox,
  InputNumber
} from 'antd';
import {
  MobileOutlined,
  UploadOutlined,
  CameraOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import AndroidDeviceSelector from '../components/AndroidDeviceSelector';
import { startAndroidAnalysis, uploadAndroidScreenshot } from '../../../services/androidApi';
import './TestCreation.css';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;
const { TextArea } = Input;
const { Option } = Select;

interface TestCreationData {
  testName: string;
  testDescription: string;
  deviceId: string;
  packageName: string;
  activityName?: string;
  screenshotData?: string;
  testType: 'functional' | 'ui' | 'performance' | 'compatibility';
  scriptFormats: string[];
  testScenarios: string[];
  executionConfig: {
    timeout: number;
    retryCount: number;
    captureScreenshots: boolean;
    recordVideo: boolean;
  };
}

const AndroidTestCreation: React.FC = () => {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [testData, setTestData] = useState<Partial<TestCreationData>>({});
  const [selectedDevice, setSelectedDevice] = useState<string | null>(null);
  const [screenshotData, setScreenshotData] = useState<string | null>(null);
  const [analysisSessionId, setAnalysisSessionId] = useState<string | null>(null);

  const steps = [
    {
      title: '基本信息',
      description: '设置测试基本信息',
      icon: <FileTextOutlined />
    },
    {
      title: '设备配置',
      description: '选择设备和应用',
      icon: <MobileOutlined />
    },
    {
      title: '界面分析',
      description: '上传截图进行分析',
      icon: <CameraOutlined />
    },
    {
      title: '测试配置',
      description: '配置测试参数',
      icon: <SettingOutlined />
    },
    {
      title: '完成创建',
      description: '生成测试脚本',
      icon: <CheckCircleOutlined />
    }
  ];

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    try {
      setLoading(true);
      const result = await uploadAndroidScreenshot(file, selectedDevice || undefined, testData.packageName);
      setScreenshotData(result.data.screenshot_data);
      message.success('截图上传成功');
      return false; // 阻止自动上传
    } catch (error) {
      message.error('截图上传失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 从设备捕获截图
  const handleCaptureScreenshot = async () => {
    if (!selectedDevice) {
      message.error('请先选择设备');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(`/api/v1/android/devices/${selectedDevice}/screenshot`, {
        method: 'POST'
      });
      
      if (response.ok) {
        const data = await response.json();
        setScreenshotData(data.data.screenshot_data);
        message.success('截图捕获成功');
      } else {
        message.error('截图捕获失败');
      }
    } catch (error) {
      message.error('截图捕获失败');
    } finally {
      setLoading(false);
    }
  };

  // 开始界面分析
  const handleStartAnalysis = async () => {
    if (!screenshotData) {
      message.error('请先上传截图或从设备捕获截图');
      return;
    }

    try {
      setLoading(true);
      const sessionId = `android_test_${Date.now()}`;
      
      const analysisRequest = {
        session_id: sessionId,
        screenshot_data: screenshotData,
        device_id: selectedDevice || '',
        package_name: testData.packageName || '',
        activity_name: testData.activityName,
        test_description: testData.testDescription || '',
        generate_formats: testData.scriptFormats || ['appium'],
        include_non_interactive: false
      };

      const result = await startAndroidAnalysis(analysisRequest);
      setAnalysisSessionId(result.session_id);
      message.success('界面分析已启动');
      setCurrentStep(4); // 跳转到完成步骤
    } catch (error) {
      message.error('启动分析失败');
    } finally {
      setLoading(false);
    }
  };

  // 下一步
  const handleNext = async () => {
    try {
      const values = await form.validateFields();
      setTestData({ ...testData, ...values });
      
      if (currentStep === 2 && screenshotData) {
        await handleStartAnalysis();
      } else {
        setCurrentStep(currentStep + 1);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 上一步
  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Card title="基本信息配置">
            <Form form={form} layout="vertical">
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="testName"
                    label="测试名称"
                    rules={[{ required: true, message: '请输入测试名称' }]}
                  >
                    <Input placeholder="输入测试名称" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="testType"
                    label="测试类型"
                    rules={[{ required: true, message: '请选择测试类型' }]}
                  >
                    <Select placeholder="选择测试类型">
                      <Option value="functional">功能测试</Option>
                      <Option value="ui">UI测试</Option>
                      <Option value="performance">性能测试</Option>
                      <Option value="compatibility">兼容性测试</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item
                name="testDescription"
                label="测试描述"
                rules={[{ required: true, message: '请输入测试描述' }]}
              >
                <TextArea 
                  rows={4} 
                  placeholder="描述测试的目的、范围和预期结果..."
                />
              </Form.Item>

              <Form.Item
                name="scriptFormats"
                label="生成脚本格式"
                initialValue={['appium']}
              >
                <Checkbox.Group>
                  <Row>
                    <Col span={8}>
                      <Checkbox value="appium">Appium (Python)</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="uiautomator2">UIAutomator2</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="midscene">Midscene</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Form>
          </Card>
        );

      case 1:
        return (
          <Card title="设备和应用配置">
            <Form form={form} layout="vertical">
              <Form.Item
                name="deviceId"
                label="选择设备"
                rules={[{ required: true, message: '请选择Android设备' }]}
              >
                <AndroidDeviceSelector
                  value={selectedDevice}
                  onChange={(deviceId) => {
                    setSelectedDevice(deviceId);
                    form.setFieldsValue({ deviceId });
                  }}
                  placeholder="选择要测试的Android设备"
                />
              </Form.Item>

              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="packageName"
                    label="应用包名"
                    rules={[{ required: true, message: '请输入应用包名' }]}
                  >
                    <Input placeholder="com.example.app" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="activityName"
                    label="Activity名称"
                  >
                    <Input placeholder="MainActivity" />
                  </Form.Item>
                </Col>
              </Row>

              {selectedDevice && (
                <Alert
                  message="设备已选择"
                  description={`设备ID: ${selectedDevice}`}
                  type="success"
                  showIcon
                  style={{ marginTop: 16 }}
                />
              )}
            </Form>
          </Card>
        );

      case 2:
        return (
          <Card title="界面截图和分析">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="界面分析"
                description="上传应用截图或从连接的设备捕获截图，AI将分析界面元素并生成测试脚本"
                type="info"
                showIcon
              />

              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Card size="small" title="上传截图">
                    <Upload
                      accept="image/*"
                      beforeUpload={handleFileUpload}
                      showUploadList={false}
                      maxCount={1}
                    >
                      <Button icon={<UploadOutlined />} block>
                        选择图片文件
                      </Button>
                    </Upload>
                  </Card>
                </Col>
                <Col xs={24} md={12}>
                  <Card size="small" title="设备截图">
                    <Button 
                      icon={<CameraOutlined />}
                      onClick={handleCaptureScreenshot}
                      disabled={!selectedDevice}
                      loading={loading}
                      block
                    >
                      从设备捕获
                    </Button>
                  </Card>
                </Col>
              </Row>

              {screenshotData && (
                <Card size="small" title="截图预览">
                  <img 
                    src={`data:image/png;base64,${screenshotData}`}
                    alt="应用截图"
                    style={{ maxWidth: '100%', maxHeight: '400px' }}
                  />
                </Card>
              )}
            </Space>
          </Card>
        );

      case 3:
        return (
          <Card title="测试执行配置">
            <Form form={form} layout="vertical">
              <Row gutter={[16, 16]}>
                <Col xs={24} md={8}>
                  <Form.Item
                    name={['executionConfig', 'timeout']}
                    label="超时时间(秒)"
                    initialValue={300}
                  >
                    <InputNumber min={30} max={3600} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name={['executionConfig', 'retryCount']}
                    label="重试次数"
                    initialValue={1}
                  >
                    <InputNumber min={0} max={5} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="执行选项">
                    <Space direction="vertical">
                      <Form.Item
                        name={['executionConfig', 'captureScreenshots']}
                        valuePropName="checked"
                        initialValue={true}
                        style={{ marginBottom: 8 }}
                      >
                        <Checkbox>捕获执行截图</Checkbox>
                      </Form.Item>
                      <Form.Item
                        name={['executionConfig', 'recordVideo']}
                        valuePropName="checked"
                        initialValue={false}
                        style={{ marginBottom: 0 }}
                      >
                        <Checkbox>录制执行视频</Checkbox>
                      </Form.Item>
                    </Space>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Card>
        );

      case 4:
        return (
          <Card title="测试创建完成">
            <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
              <RobotOutlined style={{ fontSize: '64px', color: '#52c41a' }} />
              <Title level={3}>测试创建成功！</Title>
              
              {analysisSessionId && (
                <Alert
                  message="界面分析进行中"
                  description={`分析会话ID: ${analysisSessionId}`}
                  type="success"
                  showIcon
                />
              )}

              <Paragraph>
                您的Android测试已成功创建。AI正在分析界面元素并生成自动化测试脚本。
              </Paragraph>

              <Space>
                <Button 
                  type="primary" 
                  icon={<PlayCircleOutlined />}
                  onClick={() => window.location.href = '/android/execution'}
                >
                  执行测试
                </Button>
                <Button 
                  onClick={() => window.location.href = '/android/analysis'}
                >
                  查看分析结果
                </Button>
                <Button 
                  onClick={() => {
                    setCurrentStep(0);
                    form.resetFields();
                    setTestData({});
                    setScreenshotData(null);
                    setAnalysisSessionId(null);
                  }}
                >
                  创建新测试
                </Button>
              </Space>
            </Space>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div style={{ marginBottom: '24px' }}>
          <Title level={2}>
            <RobotOutlined style={{ marginRight: '12px', color: '#52c41a' }} />
            Android测试创建
          </Title>
          <Paragraph>
            通过简单的步骤创建Android自动化测试，AI将帮助您分析界面并生成测试脚本
          </Paragraph>
        </div>

        <Card>
          <Steps current={currentStep} style={{ marginBottom: '32px' }}>
            {steps.map((step, index) => (
              <Step
                key={index}
                title={step.title}
                description={step.description}
                icon={step.icon}
              />
            ))}
          </Steps>

          {renderStepContent()}

          <Divider />

          <div style={{ textAlign: 'center' }}>
            <Space>
              {currentStep > 0 && (
                <Button onClick={handlePrev}>
                  上一步
                </Button>
              )}
              {currentStep < steps.length - 1 && (
                <Button 
                  type="primary" 
                  onClick={handleNext}
                  loading={loading}
                >
                  下一步
                </Button>
              )}
            </Space>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default AndroidTestCreation;
