/**
 * Android API服务
 * 提供Android设备管理、界面分析、脚本管理和执行等API调用
 */

import { apiClient } from './api';

// ==================== 类型定义 ====================

export interface AndroidDevice {
  id: string;
  device_id: string;
  device_name?: string;
  device_model?: string;
  manufacturer?: string;
  android_version?: string;
  api_level?: number;
  screen_resolution?: string;
  screen_density?: string;
  connection_type: string;
  status: string;
  last_seen?: string;
  created_at?: string;
}

export interface AndroidAnalysisRequest {
  session_id: string;
  screenshot_data: string;
  ui_hierarchy?: string;
  device_id?: string;
  device_info?: Record<string, any>;
  package_name?: string;
  activity_name?: string;
  test_description: string;
  additional_context?: string;
  generate_formats: string[];
  include_non_interactive: boolean;
  focus_areas?: string[];
}

export interface AndroidAnalysisResult {
  success: boolean;
  session_id: string;
  analysis_id: string;
  analysis_result: {
    app_name?: string;
    package_name?: string;
    activity_name?: string;
    screen_description: string;
    ui_elements: AndroidUIElement[];
    test_scenarios: string[];
    test_steps: AndroidTestStep[];
    analysis_summary: string;
    confidence_score: number;
  };
  generated_scripts: AndroidGeneratedScript[];
  message: string;
  processing_time: number;
  timestamp: string;
}

export interface AndroidUIElement {
  id: string;
  element_name?: string;
  element_type: string;
  element_description: string;
  resource_id?: string;
  class_name?: string;
  text?: string;
  content_desc?: string;
  bounds?: string;
  clickable: boolean;
  enabled: boolean;
  focusable: boolean;
  scrollable: boolean;
  confidence_score: number;
  is_testable: boolean;
}

export interface AndroidTestStep {
  step_number: number;
  action_type: string;
  target_element?: string;
  target_locator?: string;
  action_data?: string;
  description: string;
  expected_result?: string;
  wait_time?: number;
}

export interface AndroidGeneratedScript {
  script_type: string;
  script_language: string;
  script_content: string;
  test_description: string;
  dependencies: string[];
  setup_instructions?: string;
  execution_notes?: string;
}

export interface AndroidScript {
  id: string;
  session_id: string;
  android_analysis_id?: string;
  script_name: string;
  script_type: string;
  script_language: string;
  script_content: string;
  script_config?: Record<string, any>;
  test_description: string;
  test_scenarios?: string[];
  generated_by: string;
  generation_metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface AndroidExecution {
  id: string;
  session_id: string;
  script_id: string;
  device_id: string;
  execution_name: string;
  execution_status: string;
  start_time?: string;
  end_time?: string;
  duration_seconds?: number;
  error_message?: string;
  created_at?: string;
}

export interface AndroidExecutionRequest {
  session_id: string;
  script_id: string;
  device_id: string;
  execution_config?: Record<string, any>;
  capture_screenshots: boolean;
  record_video: boolean;
  timeout_seconds: number;
}

// ==================== 设备管理API ====================

/**
 * 扫描Android设备
 */
export const scanAndroidDevices = async (): Promise<{ success: boolean; data: AndroidDevice[]; message: string }> => {
  const response = await apiClient.get('/android/devices/scan');
  return response.data;
};

/**
 * 获取设备列表
 */
export const getAndroidDeviceList = async (): Promise<{ success: boolean; data: AndroidDevice[] }> => {
  const response = await apiClient.get('/android/devices/list');
  return response.data;
};

/**
 * 获取在线设备
 */
export const getOnlineAndroidDevices = async (): Promise<{ success: boolean; data: AndroidDevice[] }> => {
  const response = await apiClient.get('/android/devices/online');
  return response.data;
};

/**
 * 获取设备详情
 */
export const getAndroidDeviceInfo = async (deviceId: string): Promise<{ success: boolean; data: AndroidDevice }> => {
  const response = await apiClient.get(`/android/devices/${deviceId}/info`);
  return response.data;
};

/**
 * 捕获设备截图
 */
export const captureDeviceScreenshot = async (deviceId: string): Promise<{ success: boolean; data: { device_id: string; screenshot_data: string; timestamp: number } }> => {
  const response = await apiClient.post(`/android/devices/${deviceId}/screenshot`);
  return response.data;
};

/**
 * 获取UI层次结构
 */
export const getDeviceUIHierarchy = async (deviceId: string): Promise<{ success: boolean; data: { device_id: string; ui_hierarchy: string; timestamp: number } }> => {
  const response = await apiClient.post(`/android/devices/${deviceId}/ui-hierarchy`);
  return response.data;
};

/**
 * 启动应用
 */
export const startApp = async (deviceId: string, packageName: string, activityName?: string): Promise<{ success: boolean; data: any; message: string }> => {
  const response = await apiClient.post(`/android/devices/${deviceId}/start-app`, {
    package_name: packageName,
    activity_name: activityName
  });
  return response.data;
};

/**
 * 停止应用
 */
export const stopApp = async (deviceId: string, packageName: string): Promise<{ success: boolean; data: any; message: string }> => {
  const response = await apiClient.post(`/android/devices/${deviceId}/stop-app`, {
    package_name: packageName
  });
  return response.data;
};

// ==================== 界面分析API ====================

/**
 * 启动Android界面分析
 */
export const startAndroidAnalysis = async (request: AndroidAnalysisRequest): Promise<{ success: boolean; session_id: string; message: string; stream_url: string }> => {
  const response = await apiClient.post('/android/analysis/analyze', request);
  return response.data;
};

/**
 * 获取分析状态
 */
export const getAndroidAnalysisStatus = async (sessionId: string): Promise<{ success: boolean; data: any }> => {
  const response = await apiClient.get(`/android/analysis/status/${sessionId}`);
  return response.data;
};

/**
 * 获取分析结果
 */
export const getAndroidAnalysisResults = async (sessionId: string): Promise<{ success: boolean; data: any[] }> => {
  const response = await apiClient.get(`/android/analysis/results/${sessionId}`);
  return response.data;
};

/**
 * 获取Android应用列表
 */
export const getAndroidAppList = async (page: number = 1, pageSize: number = 20, search?: string): Promise<{ success: boolean; data: any[]; pagination: any }> => {
  const params = new URLSearchParams({
    page: page.toString(),
    page_size: pageSize.toString()
  });
  
  if (search) {
    params.append('search', search);
  }
  
  const response = await apiClient.get(`/android/analysis/apps?${params}`);
  return response.data;
};

/**
 * 上传Android截图
 */
export const uploadAndroidScreenshot = async (file: File, deviceId?: string, packageName?: string): Promise<{ success: boolean; data: any; message: string }> => {
  const formData = new FormData();
  formData.append('file', file);
  
  if (deviceId) {
    formData.append('device_id', deviceId);
  }
  
  if (packageName) {
    formData.append('package_name', packageName);
  }
  
  const response = await apiClient.post('/android/analysis/upload-screenshot', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
  return response.data;
};

// ==================== 脚本管理API ====================

/**
 * 生成Android脚本
 */
export const generateAndroidScript = async (request: {
  session_id: string;
  analysis_id: string;
  script_type: string;
  script_language: string;
  test_description: string;
  include_setup: boolean;
  include_teardown: boolean;
  use_page_object: boolean;
  add_assertions: boolean;
}): Promise<{ success: boolean; data: any; message: string }> => {
  const response = await apiClient.post('/android/scripts/generate', request);
  return response.data;
};

/**
 * 获取Android脚本列表
 */
export const getAndroidScriptList = async (sessionId?: string, scriptType?: string, page: number = 1, pageSize: number = 20): Promise<{ success: boolean; data: AndroidScript[]; pagination: any }> => {
  const params = new URLSearchParams({
    page: page.toString(),
    page_size: pageSize.toString()
  });
  
  if (sessionId) {
    params.append('session_id', sessionId);
  }
  
  if (scriptType) {
    params.append('script_type', scriptType);
  }
  
  const response = await apiClient.get(`/android/scripts/list?${params}`);
  return response.data;
};

/**
 * 获取Android脚本详情
 */
export const getAndroidScript = async (scriptId: string): Promise<{ success: boolean; data: AndroidScript }> => {
  const response = await apiClient.get(`/android/scripts/${scriptId}`);
  return response.data;
};

/**
 * 更新Android脚本
 */
export const updateAndroidScript = async (scriptId: string, updates: {
  script_name?: string;
  script_content?: string;
  script_config?: Record<string, any>;
  test_description?: string;
}): Promise<{ success: boolean; data: any; message: string }> => {
  const response = await apiClient.put(`/android/scripts/${scriptId}`, updates);
  return response.data;
};

/**
 * 删除Android脚本
 */
export const deleteAndroidScript = async (scriptId: string): Promise<{ success: boolean; message: string }> => {
  const response = await apiClient.delete(`/android/scripts/${scriptId}`);
  return response.data;
};

/**
 * 复制Android脚本
 */
export const duplicateAndroidScript = async (scriptId: string, newName?: string): Promise<{ success: boolean; data: any; message: string }> => {
  const response = await apiClient.post(`/android/scripts/${scriptId}/duplicate`, {
    new_name: newName
  });
  return response.data;
};

// ==================== 脚本执行API ====================

/**
 * 启动Android脚本执行
 */
export const startAndroidExecution = async (request: AndroidExecutionRequest): Promise<{ success: boolean; execution_id: string; script_id: string; device_id: string; message: string; stream_url: string }> => {
  const response = await apiClient.post('/android/execution/start', request);
  return response.data;
};

/**
 * 获取执行状态
 */
export const getAndroidExecutionStatus = async (executionId: string): Promise<{ success: boolean; data: any }> => {
  const response = await apiClient.get(`/android/execution/status/${executionId}`);
  return response.data;
};

/**
 * 获取执行结果
 */
export const getAndroidExecutionResults = async (executionId: string): Promise<{ success: boolean; data: any }> => {
  const response = await apiClient.get(`/android/execution/results/${executionId}`);
  return response.data;
};

/**
 * 停止Android脚本执行
 */
export const stopAndroidExecution = async (executionId: string): Promise<{ success: boolean; message: string }> => {
  const response = await apiClient.post(`/android/execution/stop/${executionId}`);
  return response.data;
};

/**
 * 获取执行历史
 */
export const getAndroidExecutionHistory = async (sessionId?: string, deviceId?: string, status?: string, page: number = 1, pageSize: number = 20): Promise<{ success: boolean; data: AndroidExecution[]; pagination: any }> => {
  const params = new URLSearchParams({
    page: page.toString(),
    page_size: pageSize.toString()
  });
  
  if (sessionId) {
    params.append('session_id', sessionId);
  }
  
  if (deviceId) {
    params.append('device_id', deviceId);
  }
  
  if (status) {
    params.append('status', status);
  }
  
  const response = await apiClient.get(`/android/execution/history?${params}`);
  return response.data;
};
