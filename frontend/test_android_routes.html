<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Android路由测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .route-list {
            list-style: none;
            padding: 0;
        }
        .route-item {
            margin: 10px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }
        .route-link {
            color: #1890ff;
            text-decoration: none;
            font-weight: bold;
            font-size: 16px;
        }
        .route-link:hover {
            text-decoration: underline;
        }
        .route-desc {
            color: #666;
            margin-top: 5px;
            font-size: 14px;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.implemented {
            background: #52c41a;
            color: white;
        }
        .status.placeholder {
            background: #faad14;
            color: white;
        }
        .test-info {
            background: #e6f7ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #91d5ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Android自动化测试平台 - 路由测试</h1>
        
        <div class="test-info">
            <strong>📋 测试说明：</strong><br>
            点击下面的链接测试各个Android模块的路由是否正常工作。
            绿色标签表示已实现功能，黄色标签表示占位页面。
        </div>

        <ul class="route-list">
            <li class="route-item">
                <a href="http://localhost:3001/" class="route-link">🏠 系统主页</a>
                <span class="status implemented">已实现</span>
                <div class="route-desc">系统主页，包含所有模块的导航</div>
            </li>

            <li class="route-item">
                <a href="http://localhost:3001/android" class="route-link">📱 Android主页</a>
                <span class="status implemented">已实现</span>
                <div class="route-desc">Android模块主页，功能导航和快速开始</div>
            </li>

            <li class="route-item">
                <a href="http://localhost:3001/android/analysis" class="route-link">🔍 Android界面分析</a>
                <span class="status implemented">已实现</span>
                <div class="route-desc">AI智能分析Android界面，生成测试脚本</div>
            </li>

            <li class="route-item">
                <a href="http://localhost:3001/android/devices" class="route-link">📱 Android设备管理</a>
                <span class="status implemented">已实现</span>
                <div class="route-desc">管理Android设备连接，监控设备状态</div>
            </li>

            <li class="route-item">
                <a href="http://localhost:3001/android/create" class="route-link">➕ Android测试创建</a>
                <span class="status implemented">已实现</span>
                <div class="route-desc">快速创建Android测试用例，支持步骤向导</div>
            </li>

            <li class="route-item">
                <a href="http://localhost:3001/android/scripts" class="route-link">📝 Android脚本管理</a>
                <span class="status placeholder">占位页面</span>
                <div class="route-desc">创建、编辑和管理Android自动化脚本</div>
            </li>

            <li class="route-item">
                <a href="http://localhost:3001/android/execution" class="route-link">▶️ Android脚本执行</a>
                <span class="status implemented">已实现</span>
                <div class="route-desc">执行Android自动化脚本，实时监控执行过程</div>
            </li>

            <li class="route-item">
                <a href="http://localhost:3001/android/results" class="route-link">📊 Android测试结果</a>
                <span class="status implemented">已实现</span>
                <div class="route-desc">查看和分析Android测试结果，支持详细统计</div>
            </li>

            <li class="route-item">
                <a href="http://localhost:3001/android/history" class="route-link">📈 Android执行历史</a>
                <span class="status placeholder">占位页面</span>
                <div class="route-desc">查看脚本执行历史记录</div>
            </li>

            <li class="route-item">
                <a href="http://localhost:3001/android/reports" class="route-link">📄 Android测试报告</a>
                <span class="status implemented">已实现</span>
                <div class="route-desc">生成详细的测试报告，支持多种报告类型</div>
            </li>
        </ul>

        <div class="test-info" style="margin-top: 30px;">
            <strong>🔧 后端API测试：</strong><br>
            <a href="http://localhost:8000/docs" target="_blank" style="color: #1890ff;">📚 API文档 (Swagger)</a><br>
            <a href="http://localhost:8000/api/v1/system/health" target="_blank" style="color: #1890ff;">💚 系统健康检查</a>
        </div>

        <div class="test-info" style="margin-top: 20px; background: #fff2e8; border-color: #ffd591;">
            <strong>⚠️ 注意事项：</strong><br>
            • 确保前端服务器运行在 http://localhost:3001<br>
            • 确保后端服务器运行在 http://localhost:8000<br>
            • 如果遇到路由错误，请检查浏览器控制台
        </div>
    </div>

    <script>
        // 简单的路由测试脚本
        console.log('🚀 Android路由测试页面已加载');
        console.log('前端服务器: http://localhost:3001');
        console.log('后端服务器: http://localhost:8000');
        
        // 检查服务器状态
        fetch('http://localhost:8000/api/v1/system/health')
            .then(response => response.json())
            .then(data => {
                console.log('✅ 后端服务器状态:', data);
            })
            .catch(error => {
                console.error('❌ 后端服务器连接失败:', error);
            });
    </script>
</body>
</html>
