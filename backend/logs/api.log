2025-08-20 20:10:53 | INFO     | app.api.v1.endpoints.android.android_devices:scan_android_devices:37 | 开始扫描Android设备
2025-08-20 20:10:53 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:12:40 | INFO     | app.api.v1.endpoints.android.android_devices:scan_android_devices:37 | 开始扫描Android设备
2025-08-20 20:12:40 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:14:12 | INFO     | app.api.v1.endpoints.android.android_devices:scan_android_devices:37 | 开始扫描Android设备
2025-08-20 20:14:12 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:15:45 | INFO     | app.api.v1.endpoints.android.android_devices:scan_android_devices:37 | 开始扫描Android设备
2025-08-20 20:15:45 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:28:05 | INFO     | app.api.v1.endpoints.android.android_devices:scan_android_devices:37 | 开始扫描Android设备
2025-08-20 20:28:05 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:30:38 | ERROR    | app.api.v1.endpoints.android.android_devices:capture_device_screenshot:239 | 捕获设备截图失败: 13764254B4001229, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:30:52 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_test_1755693052501
2025-08-20 20:30:52 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_test_1755693052501
2025-08-20 20:30:52 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755693052501, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:36:40 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_test_1755693400647
2025-08-20 20:36:40 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_test_1755693400647
2025-08-20 20:36:40 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755693400647, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:40:40 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_test_1755693640220
2025-08-20 20:40:40 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_test_1755693640220
2025-08-20 20:40:40 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755693640220, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:50:22 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: test_android_analysis_90437
2025-08-20 20:50:22 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: test_android_analysis_90437
2025-08-20 20:50:22 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_90437, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:50:22 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: test_android_analysis_90437
2025-08-20 20:50:22 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: test_android_analysis_90437
2025-08-20 20:52:21 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_test_1755694341283
2025-08-20 20:52:21 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_test_1755694341283
2025-08-20 20:52:21 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755694341283, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:52:53 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_analysis_1755694373481
2025-08-20 20:52:53 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_analysis_1755694373481
2025-08-20 20:52:53 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_analysis_1755694373481, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:52:53 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755694373481
2025-08-20 20:52:53 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: android_analysis_1755694373481
2025-08-20 20:52:53 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755694373481
2025-08-20 20:58:36 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: test_android_analysis_90931
2025-08-20 20:58:36 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: test_android_analysis_90931
2025-08-20 20:58:36 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_90931, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:58:36 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: test_android_analysis_90931
2025-08-20 20:58:36 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: test_android_analysis_90931
2025-08-20 21:00:48 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: test_android_analysis_91063
2025-08-20 21:00:48 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: test_android_analysis_91063
2025-08-20 21:00:48 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_91063, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:00:48 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: test_android_analysis_91063
2025-08-20 21:00:48 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: test_android_analysis_91063
2025-08-20 21:02:21 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: android_analysis_1755694373481
2025-08-20 21:02:23 | ERROR    | app.api.v1.endpoints.android.android_devices:list_android_devices:117 | 获取设备列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT android_devices.device_id, android_devices.device_name, android_devices.device_model, android_devices.manufacturer, android_devices.android_version, android_devices.api_level, android_devices.build_version, android_devices.screen_resolution, android_devices.screen_density, android_devices.cpu_architecture, android_devices.memory_total, android_devices.connection_type, android_devices.ip_address, android_devices.adb_port, android_devices.status, android_devices.last_seen, android_devices.device_config, android_devices.capabilities, android_devices.id, android_devices.created_at, android_devices.updated_at 
FROM android_devices ORDER BY android_devices.created_at DESC 
 LIMIT %s, %s]
[parameters: (0, 100)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 21:03:04 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_analysis_1755694984786
2025-08-20 21:03:04 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_analysis_1755694984786
2025-08-20 21:03:04 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_analysis_1755694984786, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:03:04 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755694984786
2025-08-20 21:03:04 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: android_analysis_1755694984786
2025-08-20 21:03:04 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755694984786
2025-08-20 21:09:48 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: test_android_analysis_91603
2025-08-20 21:09:48 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: test_android_analysis_91603
2025-08-20 21:09:48 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_91603, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:09:48 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: test_android_analysis_91603
2025-08-20 21:09:48 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: test_android_analysis_91603
2025-08-20 21:11:46 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: android_analysis_1755694984786
2025-08-20 21:12:41 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_analysis_1755695561211
2025-08-20 21:12:41 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_analysis_1755695561211
2025-08-20 21:12:41 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_analysis_1755695561211, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:12:41 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755695561211
2025-08-20 21:12:41 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: android_analysis_1755695561211
2025-08-20 21:12:41 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755695561211
