2025-08-20 16:43:51 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:43:51 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:43:51 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:43:51 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:43:51 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:43:51 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:43:51 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:43:51 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:43:51 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:43:51 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:43:51 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:43:51 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:43:51 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:43:51 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:43:51 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:43:51 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:44:01 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:44:01 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:44:01 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:44:01 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:44:01 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:44:01 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:44:01 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:44:01 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:44:02 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:44:02 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:44:02 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:44:39 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:44:39 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:44:39 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:44:39 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:44:39 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:44:47 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:44:47 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:44:47 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:44:47 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:44:47 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:44:47 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:44:47 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:44:47 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:44:47 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:44:47 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:44:47 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:44:47 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:44:47 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:44:47 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:44:47 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:44:47 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:44:56 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:44:56 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:44:57 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:44:57 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:44:57 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:44:57 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:44:57 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:44:57 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:44:57 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:44:58 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:44:58 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:47:55 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:47:55 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:47:55 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:47:55 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:47:55 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:47:58 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:47:58 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:47:58 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:47:58 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:47:58 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:47:58 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:47:58 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:47:58 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:47:58 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:47:58 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:47:58 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:47:58 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:47:58 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:47:58 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:47:58 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:47:58 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:48:07 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:48:07 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:48:08 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:48:08 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:48:08 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:48:08 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:48:08 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:48:08 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:48:08 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:48:09 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:48:09 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:48:12 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:48:12 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:48:12 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:48:12 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:48:12 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:48:12 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:48:12 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:48:12 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:48:12 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:48:12 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:48:12 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:48:12 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:48:12 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:48:12 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:48:12 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:48:12 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:48:21 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:48:21 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:48:22 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:48:22 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:48:22 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:48:22 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:48:22 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:48:22 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:48:22 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:48:23 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:48:23 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:49:18 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:49:18 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:49:18 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:49:18 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:49:18 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:49:26 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:49:26 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:49:26 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:49:26 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:49:26 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:49:26 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:49:26 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:49:26 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:49:26 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:49:26 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:49:26 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:49:26 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:49:26 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:49:26 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:49:26 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:49:26 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:49:35 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:49:35 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:49:36 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:49:36 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:49:36 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:49:36 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:49:36 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:49:36 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:49:37 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:49:37 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:49:37 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:51:28 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:51:28 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:51:28 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:51:28 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:51:28 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:51:31 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:51:31 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:51:31 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:51:31 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:51:31 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:51:31 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:51:31 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:51:31 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:51:31 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:51:31 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:51:31 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:51:31 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:51:31 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:51:31 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:51:31 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:51:31 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:51:40 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:51:40 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:51:41 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:51:41 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:51:41 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:51:41 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:51:41 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:51:41 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:51:41 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:51:42 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:51:42 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:52:37 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:52:37 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:52:37 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:52:37 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:52:37 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:52:39 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:52:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:52:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:52:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:52:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:52:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:52:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:52:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:52:39 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:52:39 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:52:39 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:52:39 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:52:39 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:52:39 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:52:39 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 202.43.239.100:3306/ai_automation
2025-08-20 16:52:39 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:53:00 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '202.43.239.100'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:53:00 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '202.43.239.100'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:53:00 | WARNING  | app.core.database_startup:initialize_database_on_startup:47 | ⚠️ 将回退到文件存储模式
2025-08-20 16:53:00 | WARNING  | app.main:init_databases:173 | ⚠️ 主数据库初始化失败，将使用文件存储
2025-08-20 16:53:00 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:53:00 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:53:00 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:53:00 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:53:21 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '202.43.239.100'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:53:21 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '202.43.239.100'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:53:21 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:53:21 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:53:25 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:53:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:53:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:53:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:53:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:53:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:53:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:53:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:53:25 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:53:25 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:53:25 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:53:25 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:53:25 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:53:25 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:53:25 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:53:25 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:53:33 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:53:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:53:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:53:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:53:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:53:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:53:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:53:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:53:33 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:53:33 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:53:33 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:53:33 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:53:33 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:53:33 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:53:33 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:53:33 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:53:34 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:53:35 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:53:35 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:53:35 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:53:35 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:53:35 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:53:35 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:53:35 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:53:36 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:53:36 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:53:36 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:53:42 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:53:42 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:53:43 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:53:43 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:53:43 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:53:43 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:53:43 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:53:43 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:53:43 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:53:44 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:53:44 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:54:50 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:54:50 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:54:50 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:54:50 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:54:50 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:54:50 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:54:50 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:54:50 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:54:50 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:54:50 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:54:52 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:54:52 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:54:52 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:54:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:54:52 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:54:52 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:54:52 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:54:52 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:54:52 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:54:52 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:54:52 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:54:52 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:54:52 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:54:52 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:54:52 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:54:52 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:54:52 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:54:52 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:54:52 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:55:01 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:55:01 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:55:01 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:55:02 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:55:02 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:55:02 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:55:02 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:55:02 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:55:02 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:55:02 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:55:02 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:55:02 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:55:02 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:55:02 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:55:02 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:55:02 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:55:03 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:55:03 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:55:03 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:55:03 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:55:03 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:55:03 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:55:27 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:55:27 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:55:27 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:55:27 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:55:27 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:55:27 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:55:27 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:55:27 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:55:27 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:55:27 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:55:30 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:55:30 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:55:30 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:55:30 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:55:30 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:55:30 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:55:30 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:55:30 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:55:30 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:55:30 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:55:30 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:55:30 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:55:30 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:55:30 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:55:30 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:55:30 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:55:30 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:55:30 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:55:39 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:55:39 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:55:39 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:55:39 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:55:40 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:55:40 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:55:40 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:55:40 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:55:40 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:55:40 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:55:40 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:55:40 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:55:40 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:55:40 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:55:40 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:55:40 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:55:40 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:55:40 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:55:41 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:55:41 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:55:41 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:55:41 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:55:46 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:55:46 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:55:46 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:55:46 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:55:46 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:55:46 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:55:46 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:55:46 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:55:46 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:55:46 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:55:46 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:55:46 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:55:46 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:55:46 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:55:46 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:55:46 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:55:55 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:55:55 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:55:56 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:55:56 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:55:56 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:55:56 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:55:56 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:55:56 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:55:56 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:55:57 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:55:57 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 17:28:13 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 17:28:13 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 17:28:13 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 17:28:13 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 17:28:13 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 18:48:51 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 18:48:51 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 18:48:51 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 18:48:51 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 18:48:51 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 18:48:51 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 18:48:51 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 18:48:51 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 18:48:51 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 18:48:51 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:48:51 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:48:51 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 18:48:51 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 18:48:51 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 18:48:51 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 18:48:51 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 18:49:06 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 18:49:06 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 18:49:06 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 18:49:06 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 18:49:06 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 18:49:06 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 18:49:06 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 18:49:06 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 18:49:06 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 18:49:06 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:49:06 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:49:06 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 18:49:06 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 18:49:06 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 18:49:06 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 18:49:06 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 18:49:13 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 18:49:14 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 18:49:14 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 18:49:14 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 18:49:14 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 18:49:14 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 18:49:14 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 18:49:14 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 18:49:15 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 18:49:15 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 18:49:15 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 18:49:17 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 18:49:17 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 18:49:18 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 18:49:18 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 18:49:18 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 18:49:18 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 18:49:18 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 18:49:18 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 18:49:18 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 18:49:19 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 18:49:19 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 18:56:09 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 18:56:09 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 18:56:09 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 18:56:09 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 18:56:09 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 18:58:21 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 18:58:21 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 18:58:21 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 18:58:21 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 18:58:21 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 18:58:21 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 18:58:21 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 18:58:21 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 18:58:21 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 18:58:21 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:58:21 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:58:21 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 18:58:21 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 18:58:21 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 18:58:21 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 18:58:21 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 18:58:32 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 18:58:32 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 18:58:33 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 18:58:33 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 18:58:33 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 18:58:33 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 18:58:33 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 18:58:33 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 18:58:34 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 18:58:34 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 18:58:34 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 18:59:25 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 18:59:25 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 18:59:25 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 18:59:25 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 18:59:25 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 18:59:25 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 18:59:25 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 18:59:25 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 18:59:25 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 18:59:25 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:59:25 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:59:25 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 18:59:25 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 18:59:25 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 18:59:25 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 18:59:25 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 18:59:37 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 18:59:37 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 18:59:38 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 18:59:38 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 18:59:38 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 18:59:38 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 18:59:38 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 18:59:38 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 18:59:38 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 18:59:39 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 18:59:39 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 19:02:44 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 19:02:44 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 19:02:44 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 19:02:44 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 19:02:44 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 19:02:47 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 19:02:47 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 19:02:47 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 19:02:47 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 19:02:47 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 19:02:47 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 19:02:47 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 19:02:47 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 19:02:47 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 19:02:47 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 19:02:47 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 19:02:47 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 19:02:47 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 19:02:47 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 19:02:47 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 19:02:47 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 19:02:57 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 19:02:58 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 19:02:58 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 19:02:58 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 19:02:58 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 19:02:58 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 19:02:58 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 19:02:58 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 19:02:59 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 19:02:59 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 19:02:59 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 19:03:00 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 19:03:00 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 19:03:00 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 19:03:00 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 19:03:00 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 19:03:02 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 19:03:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 19:03:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 19:03:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 19:03:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 19:03:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 19:03:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 19:03:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 19:03:02 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 19:03:02 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 19:03:02 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 19:03:02 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 19:03:02 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 19:03:02 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 19:03:02 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 19:03:02 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 19:03:12 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 19:03:13 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 19:03:13 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 19:03:13 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 19:03:13 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 19:03:13 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 19:03:13 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 19:03:13 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 19:03:14 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 19:03:14 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 19:03:14 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 19:03:34 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 19:03:34 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 19:03:34 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 19:03:34 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 19:03:34 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 19:03:46 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 19:03:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 19:03:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 19:03:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 19:03:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 19:03:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 19:03:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 19:03:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 19:03:46 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 19:03:46 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 19:03:46 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 19:03:46 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 19:03:46 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 19:03:46 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 19:03:46 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 19:03:46 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 19:03:57 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 19:03:57 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 19:03:58 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 19:03:58 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 19:03:58 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 19:03:58 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 19:03:58 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 19:03:58 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 19:03:58 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 19:03:59 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 19:03:59 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 19:21:38 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 19:21:38 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 19:21:39 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 19:21:39 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 19:21:39 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 19:21:43 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 19:21:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 19:21:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 19:21:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 19:21:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 19:21:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 19:21:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 19:21:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 19:21:43 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 19:21:43 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 19:21:43 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 19:21:43 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 19:21:43 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 19:21:43 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 19:21:43 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 19:21:43 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 19:21:54 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 19:21:54 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 19:21:55 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 19:21:55 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 19:21:55 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 19:21:55 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 19:21:55 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 19:21:55 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 19:21:55 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 19:21:56 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 19:21:56 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:09:15 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:09:15 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:09:15 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:09:15 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:09:15 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:09:24 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:09:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:09:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:09:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:09:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:09:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:09:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:09:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:09:24 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:09:24 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:09:24 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:09:24 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:09:24 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:09:24 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:09:24 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:09:24 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:09:35 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:09:36 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:09:36 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:09:36 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:09:36 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:09:36 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:09:37 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:09:37 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:09:37 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:09:38 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:09:38 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:09:50 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:09:50 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:09:50 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:09:50 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:09:50 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:10:09 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:10:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:10:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:10:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:10:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:10:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:10:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:10:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:10:09 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:10:09 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:10:09 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:10:09 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:10:09 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:10:09 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:10:09 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:10:10 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:10:21 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:10:21 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:10:22 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:10:22 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:10:22 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:10:22 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:10:23 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:10:23 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:10:23 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:10:24 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:10:24 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:10:53 | INFO     | app.api.v1.endpoints.android.android_devices:scan_android_devices:37 | 开始扫描Android设备
2025-08-20 20:10:53 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:11:35 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:11:35 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:11:35 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:11:35 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:11:35 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:11:37 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:11:37 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:11:37 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:11:37 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:11:37 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:11:37 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:11:37 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:11:37 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:11:37 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:11:37 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:11:37 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:11:37 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:11:37 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:11:37 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:11:37 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:11:37 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:11:48 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:11:49 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:11:49 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:11:49 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:11:49 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:11:49 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:11:49 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:11:49 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:11:50 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:11:50 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:11:50 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:12:01 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:12:01 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:12:01 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:12:01 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:12:01 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:12:01 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:12:01 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:12:01 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:12:01 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:12:01 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:12:01 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:12:01 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:12:01 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:12:01 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:12:01 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:12:01 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:12:12 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:12:13 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:12:13 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:12:13 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:12:13 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:12:13 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:12:14 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:12:14 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:12:14 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:12:15 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:12:15 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:12:40 | INFO     | app.api.v1.endpoints.android.android_devices:scan_android_devices:37 | 开始扫描Android设备
2025-08-20 20:12:40 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:13:10 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:13:10 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:13:10 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:13:10 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:13:10 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:13:12 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:13:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:13:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:13:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:13:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:13:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:13:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:13:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:13:12 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:13:12 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:13:12 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:13:12 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:13:12 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:13:12 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:13:12 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:13:12 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:13:22 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:13:22 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:13:23 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:13:23 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:13:23 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:13:23 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:13:23 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:13:23 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:13:24 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:13:24 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:13:24 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:13:36 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:13:36 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:13:36 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:13:36 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:13:36 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:13:36 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:13:36 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:13:36 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:13:36 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:13:36 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:13:36 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:13:36 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:13:36 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:13:36 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:13:36 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:13:36 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:13:48 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:13:49 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:13:49 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:13:49 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:13:49 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:13:49 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:13:49 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:13:49 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:13:50 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:13:51 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:13:51 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:14:12 | INFO     | app.api.v1.endpoints.android.android_devices:scan_android_devices:37 | 开始扫描Android设备
2025-08-20 20:14:12 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:14:28 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:14:28 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:14:28 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:14:28 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:14:28 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:14:31 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:14:31 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:14:31 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:14:31 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:14:31 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:14:31 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:14:31 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:14:31 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:14:31 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:14:31 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:14:31 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:14:31 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:14:31 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:14:31 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:14:31 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:14:31 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:14:41 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:14:41 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:14:42 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:14:42 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:14:42 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:14:42 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:14:42 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:14:42 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:14:42 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:14:43 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:14:43 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:14:47 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:14:47 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:14:47 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:14:47 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:14:47 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:14:49 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:14:49 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:14:49 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:14:49 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:14:49 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:14:49 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:14:49 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:14:49 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:14:49 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:14:49 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:14:49 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:14:49 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:14:49 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:14:49 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:14:49 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:14:49 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:15:00 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:15:00 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:15:01 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:15:01 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:15:01 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:15:01 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:15:01 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:15:01 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:15:01 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:15:02 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:15:02 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:15:15 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:15:15 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:15:15 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:15:15 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:15:15 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:15:15 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:15:15 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:15:15 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:15:15 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:15:15 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:15:15 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:15:15 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:15:15 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:15:15 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:15:15 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:15:15 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:15:25 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:15:26 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:15:26 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:15:26 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:15:26 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:15:26 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:15:26 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:15:26 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:15:27 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:15:27 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:15:27 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:15:45 | INFO     | app.api.v1.endpoints.android.android_devices:scan_android_devices:37 | 开始扫描Android设备
2025-08-20 20:15:45 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:16:42 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:16:42 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:16:42 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:16:42 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:16:42 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:16:45 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:16:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:16:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:16:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:16:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:16:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:16:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:16:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:16:45 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:16:45 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:16:45 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:16:45 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:16:45 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:16:45 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:16:45 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:16:45 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:16:56 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:16:56 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:16:57 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:16:57 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:16:57 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:16:57 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:16:57 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:16:57 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:16:57 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:16:58 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:16:58 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:17:14 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:17:14 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:17:14 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:17:14 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:17:14 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:17:16 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:17:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:17:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:17:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:17:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:17:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:17:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:17:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:17:16 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:17:16 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:17:16 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:17:16 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:17:16 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:17:16 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:17:16 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:17:17 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:17:27 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:17:28 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:17:28 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:17:28 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:17:28 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:17:28 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:17:28 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:17:28 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:17:29 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:17:29 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:17:29 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:17:41 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:17:41 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:17:41 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:17:41 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:17:41 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:17:43 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:17:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:17:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:17:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:17:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:17:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:17:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:17:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:17:43 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:17:43 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:17:43 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:17:43 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:17:43 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:17:43 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:17:43 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:17:43 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:17:54 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:17:54 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:17:55 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:17:55 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:17:55 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:17:55 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:17:55 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:17:55 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:17:56 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:17:56 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:17:56 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:17:59 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:17:59 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:17:59 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:17:59 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:17:59 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:18:02 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:18:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:18:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:18:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:18:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:18:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:18:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:18:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:18:02 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:18:02 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:18:02 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:18:02 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:18:02 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:18:02 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:18:02 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:18:02 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:18:14 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:18:14 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:18:15 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:18:15 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:18:15 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:18:15 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:18:15 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:18:15 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:18:15 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:18:16 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:18:16 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:22:29 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:22:29 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:22:29 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:22:29 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:22:29 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:22:32 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:22:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:22:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:22:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:22:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:22:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:22:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:22:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:22:32 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:22:32 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:22:32 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:22:32 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:22:32 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:22:32 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:22:32 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:22:32 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:22:43 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:22:43 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:22:44 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:22:44 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:22:44 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:22:44 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:22:44 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:22:44 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:22:44 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:22:45 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:22:45 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:23:13 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:23:13 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:23:13 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:23:13 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:23:13 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:23:16 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:23:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:23:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:23:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:23:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:23:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:23:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:23:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:23:16 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:23:16 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:23:16 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:23:16 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:23:16 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:23:16 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:23:16 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:23:16 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:23:26 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:23:26 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:23:27 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:23:27 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:23:27 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:23:27 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:23:27 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:23:27 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:23:28 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:23:28 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:23:28 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:23:37 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:23:37 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:23:37 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:23:37 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:23:37 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:23:40 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:23:40 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:23:40 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:23:40 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:23:40 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:23:40 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:23:40 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:23:40 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:23:40 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:23:40 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:23:40 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:23:40 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:23:40 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:23:40 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:23:40 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:23:40 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:23:51 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:23:51 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:23:52 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:23:52 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:23:52 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:23:52 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:23:52 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:23:52 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:23:53 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:23:53 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:23:53 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:24:04 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:24:04 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:24:04 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:24:04 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:24:04 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:24:08 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:24:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:24:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:24:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:24:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:24:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:24:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:24:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:24:08 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:24:08 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:24:08 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:24:08 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:24:08 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:24:08 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:24:08 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:24:08 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:24:20 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:24:20 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:24:21 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:24:21 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:24:21 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:24:21 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:24:21 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:24:21 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:24:21 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:24:22 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:24:22 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:24:35 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:24:35 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:24:35 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:24:35 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:24:35 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:24:38 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:24:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:24:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:24:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:24:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:24:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:24:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:24:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:24:38 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:24:38 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:24:38 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:24:38 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:24:38 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:24:38 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:24:38 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:24:38 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:24:49 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:24:49 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:24:50 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:24:50 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:24:50 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:24:50 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:24:50 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:24:50 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:24:50 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:24:51 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:24:51 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:24:57 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:24:57 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:24:57 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:24:57 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:24:57 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:24:59 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:24:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:24:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:24:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:24:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:24:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:24:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:24:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:24:59 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:24:59 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:24:59 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:24:59 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:24:59 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:24:59 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:24:59 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:24:59 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:25:10 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:25:10 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:25:11 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:25:11 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:25:11 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:25:11 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:25:11 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:25:11 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:25:11 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:25:12 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:25:12 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:25:28 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:25:28 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:25:28 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:25:28 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:25:28 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:25:30 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:25:30 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:25:30 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:25:30 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:25:30 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:25:30 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:25:30 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:25:30 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:25:30 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:25:30 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:25:30 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:25:30 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:25:30 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:25:30 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:25:30 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:25:30 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:25:42 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:25:42 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:25:43 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:25:43 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:25:43 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:25:43 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:25:43 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:25:43 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:25:44 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:25:44 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:25:44 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:26:05 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:26:05 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:26:05 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:26:05 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:26:05 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:26:08 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:26:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:26:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:26:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:26:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:26:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:26:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:26:08 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:26:08 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:26:08 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:26:08 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:26:08 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:26:08 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:26:08 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:26:08 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:26:08 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:26:18 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:26:18 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:26:19 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:26:19 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:26:19 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:26:19 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:26:19 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:26:19 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:26:19 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:26:20 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:26:20 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:26:24 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:26:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:26:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:26:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:26:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:26:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:26:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:26:24 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:26:24 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:26:24 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:26:24 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:26:24 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:26:24 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:26:24 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:26:24 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:26:24 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:26:35 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:26:35 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:26:36 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:26:36 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:26:36 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:26:36 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:26:36 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:26:36 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:26:36 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:26:37 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:26:37 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:27:16 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:27:16 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:27:16 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:27:16 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:27:16 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:27:18 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:27:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:27:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:27:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:27:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:27:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:27:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:27:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:27:18 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:27:18 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:27:18 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:27:18 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:27:18 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:27:18 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:27:18 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:27:18 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:27:29 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:27:29 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:27:29 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:27:29 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:27:30 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:27:30 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:27:30 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:27:30 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:27:30 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:27:31 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:27:31 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:28:05 | INFO     | app.api.v1.endpoints.android.android_devices:scan_android_devices:37 | 开始扫描Android设备
2025-08-20 20:28:05 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:30:18 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:30:18 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:30:18 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:30:18 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:30:18 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:30:20 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:30:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:30:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:30:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:30:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:30:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:30:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:30:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:30:20 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:30:20 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:30:20 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:30:20 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:30:20 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:30:20 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:30:20 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:30:20 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:30:31 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:30:32 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:30:32 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:30:32 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:30:32 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:30:32 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:30:32 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:30:32 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:30:33 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:30:33 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:30:33 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:30:38 | ERROR    | app.api.v1.endpoints.android.android_devices:capture_device_screenshot:239 | 捕获设备截图失败: 13764254B4001229, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:30:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:30:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:30:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:30:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:30:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:30:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:30:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:30:46 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/android/screenshots
2025-08-20 20:30:46 | INFO     | app.utils.file_utils:save_uploaded_file:344 | 文件保存成功: uploads\android\screenshots\20250820_203046_5502db5a.jpg
2025-08-20 20:30:46 | INFO     | app.utils.image_utils:encode_image_to_base64:27 | 图像编码成功: uploads\android\screenshots\20250820_203046_5502db5a.jpg
2025-08-20 20:30:52 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_test_1755693052501
2025-08-20 20:30:52 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_test_1755693052501
2025-08-20 20:30:52 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755693052501, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:30:52 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:30:52 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:30:52 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:30:52 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:30:52 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:30:55 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:30:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:30:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:30:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:30:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:30:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:30:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:30:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:30:55 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:30:55 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:30:55 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:30:55 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:30:55 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:30:55 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:30:55 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:30:55 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:31:06 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:31:06 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:31:07 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:31:07 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:31:07 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:31:07 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:31:07 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:31:07 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:31:08 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:31:08 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:31:08 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:31:12 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:31:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:31:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:31:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:31:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:31:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:31:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:31:12 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:31:12 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:31:12 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:31:12 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:31:12 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:31:12 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:31:12 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:31:12 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:31:12 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:31:23 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:31:23 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:31:24 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:31:24 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:31:24 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:31:24 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:31:24 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:31:24 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:31:24 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:31:25 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:31:25 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:31:29 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:31:29 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:31:29 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:31:29 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:31:29 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:31:32 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:31:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:31:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:31:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:31:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:31:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:31:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:31:32 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:31:32 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:31:32 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:31:32 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:31:32 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:31:32 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:31:32 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:31:32 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:31:32 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:31:43 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:31:43 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:31:44 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:31:44 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:31:44 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:31:44 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:31:44 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:31:44 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:31:44 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:31:45 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:31:45 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:31:52 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:31:52 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:31:52 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:31:52 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:31:52 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:31:55 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:31:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:31:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:31:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:31:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:31:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:31:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:31:55 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:31:55 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:31:55 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:31:55 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:31:55 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:31:55 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:31:55 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:31:55 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:31:55 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:32:05 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:32:05 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:32:06 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:32:06 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:32:06 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:32:06 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:32:06 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:32:06 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:32:06 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:32:07 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:32:07 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:36:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:36:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:36:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:36:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:36:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:36:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:36:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:36:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/android/screenshots
2025-08-20 20:36:38 | INFO     | app.utils.file_utils:save_uploaded_file:344 | 文件保存成功: uploads\android\screenshots\20250820_203638_e4d42da8.jpg
2025-08-20 20:36:38 | INFO     | app.utils.image_utils:encode_image_to_base64:27 | 图像编码成功: uploads\android\screenshots\20250820_203638_e4d42da8.jpg
2025-08-20 20:36:40 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_test_1755693400647
2025-08-20 20:36:40 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_test_1755693400647
2025-08-20 20:36:40 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755693400647, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:39:26 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:39:26 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:39:26 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:39:26 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:39:26 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:39:26 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:39:26 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:39:26 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/android/screenshots
2025-08-20 20:39:26 | INFO     | app.utils.file_utils:save_uploaded_file:344 | 文件保存成功: uploads\android\screenshots\20250820_203926_75398fb5.jpg
2025-08-20 20:39:26 | INFO     | app.utils.image_utils:encode_image_to_base64:27 | 图像编码成功: uploads\android\screenshots\20250820_203926_75398fb5.jpg
2025-08-20 20:40:40 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_test_1755693640220
2025-08-20 20:40:40 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_test_1755693640220
2025-08-20 20:40:40 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755693640220, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:50:10 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:50:10 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:50:10 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:50:10 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:50:10 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:50:16 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:50:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:50:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:50:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:50:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:50:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:50:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:50:16 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:50:16 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:50:16 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:50:16 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:50:16 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:50:16 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:50:16 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:50:16 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:50:16 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:50:22 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: test_android_analysis_90437
2025-08-20 20:50:22 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: test_android_analysis_90437
2025-08-20 20:50:22 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_90437, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:50:22 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: test_android_analysis_90437
2025-08-20 20:50:22 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: test_android_analysis_90437
2025-08-20 20:50:27 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:50:28 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:50:28 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:50:28 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:50:28 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:50:28 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:50:28 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:50:28 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:50:29 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:50:29 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:50:29 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:52:19 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:52:19 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:52:19 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:52:19 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:52:19 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:52:19 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:52:19 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:52:19 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/android/screenshots
2025-08-20 20:52:19 | INFO     | app.utils.file_utils:save_uploaded_file:344 | 文件保存成功: uploads\android\screenshots\20250820_205219_37ae82e0.jpg
2025-08-20 20:52:19 | INFO     | app.utils.image_utils:encode_image_to_base64:27 | 图像编码成功: uploads\android\screenshots\20250820_205219_37ae82e0.jpg
2025-08-20 20:52:21 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_test_1755694341283
2025-08-20 20:52:21 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_test_1755694341283
2025-08-20 20:52:21 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755694341283, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:52:53 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_analysis_1755694373481
2025-08-20 20:52:53 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_analysis_1755694373481
2025-08-20 20:52:53 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_analysis_1755694373481, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:52:53 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755694373481
2025-08-20 20:52:53 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: android_analysis_1755694373481
2025-08-20 20:52:53 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755694373481
2025-08-20 20:54:58 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:54:58 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:54:58 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:54:58 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:54:58 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:55:02 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:55:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:55:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:55:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:55:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:55:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:55:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:55:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:55:02 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:55:02 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:55:02 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:55:02 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:55:02 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:55:02 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:55:02 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:55:02 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:55:13 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:55:13 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:55:14 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:55:14 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:55:14 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:55:14 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:55:14 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:55:14 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:55:14 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:55:15 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:55:15 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:55:40 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:55:40 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:55:40 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:55:40 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:55:40 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:55:43 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:55:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:55:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:55:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:55:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:55:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:55:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:55:43 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:55:43 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:55:43 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:55:43 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:55:43 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:55:43 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:55:43 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:55:43 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:55:43 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:55:54 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:55:54 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:55:54 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:55:54 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:55:54 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:55:54 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:55:55 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:55:55 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:55:55 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:55:56 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:55:56 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:55:59 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:55:59 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:55:59 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:55:59 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:55:59 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:56:02 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:56:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:56:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:56:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:56:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:56:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:56:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:56:02 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:56:02 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:56:02 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:56:02 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:56:02 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:56:02 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:56:02 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:56:02 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:56:02 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:56:12 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:56:13 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:56:13 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:56:13 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:56:13 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:56:13 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:56:13 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:56:13 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:56:14 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:56:14 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:56:14 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:56:20 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:56:20 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:56:20 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:56:20 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:56:20 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:56:23 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:56:23 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:56:23 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:56:23 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:56:23 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:56:23 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:56:23 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:56:23 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:56:23 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:56:23 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:56:23 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:56:23 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:56:23 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:56:23 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:56:23 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:56:23 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:56:34 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:56:35 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:56:35 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:56:35 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:56:35 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:56:35 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:56:35 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:56:35 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:56:36 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:56:36 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:56:36 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:56:42 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:56:42 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:56:42 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:56:42 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:56:42 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:56:45 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:56:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:56:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:56:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:56:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:56:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:56:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:56:45 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:56:45 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:56:45 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:56:45 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:56:45 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:56:45 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:56:45 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:56:45 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:56:45 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:56:56 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:56:57 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:56:57 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:56:57 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:56:57 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:56:57 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:56:57 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:56:57 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:56:58 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:56:58 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:56:58 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:57:07 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:57:07 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:57:07 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:57:07 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:57:07 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:57:10 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:57:10 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:57:10 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:57:10 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:57:10 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:57:10 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:57:10 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:57:10 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:57:10 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:57:10 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:57:10 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:57:10 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:57:10 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:57:10 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:57:10 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:57:10 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:57:21 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:57:21 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:57:22 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:57:22 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:57:22 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:57:22 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:57:22 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:57:22 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:57:22 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:57:23 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:57:23 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:57:34 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:57:34 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:57:34 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:57:34 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:57:34 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:57:38 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:57:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:57:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:57:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:57:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:57:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:57:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:57:38 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:57:38 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:57:38 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:57:38 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:57:38 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:57:38 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:57:38 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:57:38 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:57:38 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:57:48 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:57:49 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:57:49 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:57:49 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:57:49 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:57:49 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:57:49 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:57:49 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:57:50 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:57:50 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:57:50 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:57:56 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:57:56 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:57:56 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:57:56 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:57:56 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:57:59 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:57:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:57:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:57:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:57:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:57:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:57:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:57:59 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:57:59 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:57:59 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:57:59 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:57:59 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:57:59 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:57:59 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:57:59 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:57:59 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:58:10 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:58:10 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:58:10 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:58:10 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:58:10 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:58:10 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:58:11 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:58:11 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:58:11 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:58:12 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:58:12 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:58:17 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:58:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:58:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:58:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:58:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:58:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:58:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:58:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:58:17 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:58:17 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:58:17 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:58:17 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:58:17 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:58:17 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:58:17 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:58:18 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 20:58:29 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 20:58:30 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 20:58:30 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 20:58:30 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 20:58:30 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 20:58:30 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 20:58:30 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 20:58:30 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 20:58:31 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 20:58:31 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 20:58:31 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 20:58:36 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: test_android_analysis_90931
2025-08-20 20:58:36 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: test_android_analysis_90931
2025-08-20 20:58:36 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_90931, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:58:36 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: test_android_analysis_90931
2025-08-20 20:58:36 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: test_android_analysis_90931
2025-08-20 20:59:47 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 20:59:47 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:59:47 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 20:59:47 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 20:59:47 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 20:59:50 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 20:59:50 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 20:59:50 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 20:59:50 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 20:59:50 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 20:59:50 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 20:59:50 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 20:59:50 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 20:59:50 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 20:59:50 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:59:50 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 20:59:50 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 20:59:50 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 20:59:50 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 20:59:50 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 20:59:50 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 21:00:00 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 21:00:01 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 21:00:01 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 21:00:01 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 21:00:01 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 21:00:01 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 21:00:01 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 21:00:01 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 21:00:02 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 21:00:02 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 21:00:02 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 21:00:07 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 21:00:07 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 21:00:07 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 21:00:07 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 21:00:07 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 21:00:07 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 21:00:07 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 21:00:07 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 21:00:07 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 21:00:07 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:00:07 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:00:07 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 21:00:07 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 21:00:07 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 21:00:07 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 21:00:07 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 21:00:18 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 21:00:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 21:00:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 21:00:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 21:00:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 21:00:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 21:00:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 21:00:18 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 21:00:18 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 21:00:18 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:00:18 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:00:18 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 21:00:18 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 21:00:18 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 21:00:18 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 21:00:18 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 21:00:29 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 21:00:29 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 21:00:30 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 21:00:30 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 21:00:30 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 21:00:30 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 21:00:30 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 21:00:30 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 21:00:30 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 21:00:31 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 21:00:31 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 21:00:48 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: test_android_analysis_91063
2025-08-20 21:00:48 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: test_android_analysis_91063
2025-08-20 21:00:48 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_91063, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:00:48 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: test_android_analysis_91063
2025-08-20 21:00:48 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: test_android_analysis_91063
2025-08-20 21:02:21 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: android_analysis_1755694373481
2025-08-20 21:02:23 | ERROR    | app.database.repositories.base:get_all:68 | 获取AndroidDevice列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT android_devices.device_id, android_devices.device_name, android_devices.device_model, android_devices.manufacturer, android_devices.android_version, android_devices.api_level, android_devices.build_version, android_devices.screen_resolution, android_devices.screen_density, android_devices.cpu_architecture, android_devices.memory_total, android_devices.connection_type, android_devices.ip_address, android_devices.adb_port, android_devices.status, android_devices.last_seen, android_devices.device_config, android_devices.capabilities, android_devices.id, android_devices.created_at, android_devices.updated_at 
FROM android_devices ORDER BY android_devices.created_at DESC 
 LIMIT %s, %s]
[parameters: (0, 100)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 21:02:23 | ERROR    | app.api.v1.endpoints.android.android_devices:list_android_devices:117 | 获取设备列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT android_devices.device_id, android_devices.device_name, android_devices.device_model, android_devices.manufacturer, android_devices.android_version, android_devices.api_level, android_devices.build_version, android_devices.screen_resolution, android_devices.screen_density, android_devices.cpu_architecture, android_devices.memory_total, android_devices.connection_type, android_devices.ip_address, android_devices.adb_port, android_devices.status, android_devices.last_seen, android_devices.device_config, android_devices.capabilities, android_devices.id, android_devices.created_at, android_devices.updated_at 
FROM android_devices ORDER BY android_devices.created_at DESC 
 LIMIT %s, %s]
[parameters: (0, 100)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 21:03:04 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_analysis_1755694984786
2025-08-20 21:03:04 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_analysis_1755694984786
2025-08-20 21:03:04 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_analysis_1755694984786, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:03:04 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755694984786
2025-08-20 21:03:04 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: android_analysis_1755694984786
2025-08-20 21:03:04 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755694984786
2025-08-20 21:04:54 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 21:04:54 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 21:04:54 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 21:04:54 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 21:04:54 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 21:09:09 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 21:09:09 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 21:09:09 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 21:09:09 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:09:09 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:09:09 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:09:09 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:09:09 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 21:09:09 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 21:09:09 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 21:09:09 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 21:09:09 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 21:09:09 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 21:09:09 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 21:09:09 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 21:09:09 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 21:09:09 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 21:09:09 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 21:09:09 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:09:09 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:09:09 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 21:09:09 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 21:09:09 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 21:09:09 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 21:09:09 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 21:09:09 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 21:09:09 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 21:09:17 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 21:09:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 21:09:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 21:09:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 21:09:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 21:09:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 21:09:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 21:09:17 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 21:09:17 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 21:09:17 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:09:17 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:09:17 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 21:09:17 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 21:09:17 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 21:09:17 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 21:09:17 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 21:09:22 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 21:09:22 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 21:09:22 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 21:09:22 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 21:09:22 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 21:09:22 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 21:09:22 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 21:09:22 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 21:09:22 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 21:09:22 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 21:09:22 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 21:09:22 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 21:09:23 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 21:09:23 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 21:09:23 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 21:09:23 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 21:09:23 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 21:09:23 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 21:09:23 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 21:09:23 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 21:09:23 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 21:09:23 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 21:09:23 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 21:09:24 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 21:09:24 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 21:09:25 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 21:09:25 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 21:09:25 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 21:09:25 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 21:09:25 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 21:09:25 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 21:09:25 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 21:09:30 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 21:09:30 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 21:09:30 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 21:09:30 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 21:09:30 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 21:09:30 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 21:09:31 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 21:09:31 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 21:09:31 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 21:09:32 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 21:09:32 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 21:09:48 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: test_android_analysis_91603
2025-08-20 21:09:48 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: test_android_analysis_91603
2025-08-20 21:09:48 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_91603, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:09:48 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: test_android_analysis_91603
2025-08-20 21:09:48 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: test_android_analysis_91603
2025-08-20 21:11:46 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: android_analysis_1755694984786
2025-08-20 21:12:41 | INFO     | app.api.v1.endpoints.android.android_analysis:analyze_android_interface:53 | 开始Android界面分析: android_analysis_1755695561211
2025-08-20 21:12:41 | INFO     | app.api.v1.endpoints.android.android_analysis:process_android_analysis:104 | 开始处理Android界面分析: android_analysis_1755695561211
2025-08-20 21:12:41 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_analysis_1755695561211, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:12:41 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755695561211
2025-08-20 21:12:41 | INFO     | app.api.v1.endpoints.android.android_analysis:event_generator:273 | SSE流结束: android_analysis_1755695561211
2025-08-20 21:12:41 | INFO     | app.api.v1.endpoints.android.android_analysis:stream_android_analysis:232 | 开始Android分析SSE流: android_analysis_1755695561211
2025-08-20 21:14:37 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 21:14:37 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 21:14:37 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 21:14:37 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 21:14:37 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
