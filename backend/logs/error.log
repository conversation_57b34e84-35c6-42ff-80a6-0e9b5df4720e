2025-08-20 16:44:39 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:47:55 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:49:18 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:51:28 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:52:37 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:53:00 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '202.43.239.100'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:53:00 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '202.43.239.100'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:53:21 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '202.43.239.100'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:53:21 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '202.43.239.100'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:54:50 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:54:50 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:55:27 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:55:27 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 17:28:13 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 18:56:09 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 19:02:44 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 19:03:00 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 19:03:34 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 19:21:38 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:09:15 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:09:50 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:10:53 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:11:35 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:12:40 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:13:10 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:14:12 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:14:28 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:14:47 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:15:45 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:16:42 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:17:14 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:17:41 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:17:59 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:22:29 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:23:13 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:23:37 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:24:04 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:24:35 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:24:57 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:25:28 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:26:05 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:27:16 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:28:05 | ERROR    | app.api.v1.endpoints.android.android_devices:scan_android_devices:79 | 扫描Android设备失败: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:30:18 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:30:38 | ERROR    | app.api.v1.endpoints.android.android_devices:capture_device_screenshot:239 | 捕获设备截图失败: 13764254B4001229, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:30:52 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755693052501, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:30:52 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:31:29 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:31:52 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:36:40 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755693400647, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:40:40 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755693640220, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:50:10 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:50:22 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_90437, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:52:21 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_test_1755694341283, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:52:53 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_analysis_1755694373481, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:54:58 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:55:40 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:55:59 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:56:20 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:56:42 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:57:07 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:57:34 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:57:56 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 20:58:36 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_90931, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 20:59:47 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 21:00:48 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_91063, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:02:23 | ERROR    | app.database.repositories.base:get_all:68 | 获取AndroidDevice列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT android_devices.device_id, android_devices.device_name, android_devices.device_model, android_devices.manufacturer, android_devices.android_version, android_devices.api_level, android_devices.build_version, android_devices.screen_resolution, android_devices.screen_density, android_devices.cpu_architecture, android_devices.memory_total, android_devices.connection_type, android_devices.ip_address, android_devices.adb_port, android_devices.status, android_devices.last_seen, android_devices.device_config, android_devices.capabilities, android_devices.id, android_devices.created_at, android_devices.updated_at 
FROM android_devices ORDER BY android_devices.created_at DESC 
 LIMIT %s, %s]
[parameters: (0, 100)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 21:02:23 | ERROR    | app.api.v1.endpoints.android.android_devices:list_android_devices:117 | 获取设备列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT android_devices.device_id, android_devices.device_name, android_devices.device_model, android_devices.manufacturer, android_devices.android_version, android_devices.api_level, android_devices.build_version, android_devices.screen_resolution, android_devices.screen_density, android_devices.cpu_architecture, android_devices.memory_total, android_devices.connection_type, android_devices.ip_address, android_devices.adb_port, android_devices.status, android_devices.last_seen, android_devices.device_config, android_devices.capabilities, android_devices.id, android_devices.created_at, android_devices.updated_at 
FROM android_devices ORDER BY android_devices.created_at DESC 
 LIMIT %s, %s]
[parameters: (0, 100)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 21:03:04 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_analysis_1755694984786, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:04:54 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 21:09:48 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: test_android_analysis_91603, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:12:41 | ERROR    | app.api.v1.endpoints.android.android_analysis:process_android_analysis:192 | Android界面分析失败: android_analysis_1755695561211, 错误: RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 21:14:37 | ERROR    | app.main:cleanup_resources:296 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
