#!/usr/bin/env python3
"""
插入模拟Android设备数据到数据库
用于测试设备列表API
"""
import asyncio
import sys
import os
from datetime import datetime
from sqlalchemy import select

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.connection import get_database
from app.database.models.android_analysis import AndroidDevice

async def insert_mock_devices():
    """插入模拟设备数据"""
    try:
        # 获取数据库会话
        async for session in get_database():
            # 模拟设备数据
            mock_devices = [
                {
                    "device_id": "emulator-5554",
                    "device_name": "Android Emulator",
                    "device_model": "Android SDK built for x86",
                    "manufacturer": "Google",
                    "android_version": "11",
                    "api_level": 30,
                    "build_version": "RSR1.201013.001",
                    "screen_resolution": "1080x1920",
                    "screen_density": "420",
                    "cpu_architecture": "x86",
                    "memory_total": "2048MB",
                    "connection_type": "emulator",
                    "ip_address": "127.0.0.1",
                    "adb_port": 5554,
                    "status": "online",
                    "last_seen": datetime.now(),
                    "capabilities": ["screenshot", "ui_hierarchy", "app_management"],
                    "device_config": {
                        "supports_uiautomator": True,
                        "supports_appium": True,
                        "supports_screenshot": True,
                        "is_emulator": True
                    }
                },
                {
                    "device_id": "test_device_001",
                    "device_name": "Samsung Galaxy S21",
                    "device_model": "SM-G991B",
                    "manufacturer": "Samsung",
                    "android_version": "12",
                    "api_level": 31,
                    "build_version": "SP1A.210812.016",
                    "screen_resolution": "1080x2400",
                    "screen_density": "420",
                    "cpu_architecture": "arm64-v8a",
                    "memory_total": "8192MB",
                    "connection_type": "usb",
                    "status": "online",
                    "last_seen": datetime.now(),
                    "capabilities": ["screenshot", "ui_hierarchy", "app_management"],
                    "device_config": {
                        "supports_uiautomator": True,
                        "supports_appium": True,
                        "supports_screenshot": True,
                        "is_emulator": False
                    }
                },
                {
                    "device_id": "*************:5555",
                    "device_name": "Xiaomi Mi 11",
                    "device_model": "M2011K2C",
                    "manufacturer": "Xiaomi",
                    "android_version": "13",
                    "api_level": 33,
                    "build_version": "TKQ1.220829.002",
                    "screen_resolution": "1440x3200",
                    "screen_density": "560",
                    "cpu_architecture": "arm64-v8a",
                    "memory_total": "12288MB",
                    "connection_type": "wifi",
                    "ip_address": "*************",
                    "adb_port": 5555,
                    "status": "online",
                    "last_seen": datetime.now(),
                    "capabilities": ["screenshot", "ui_hierarchy", "app_management"],
                    "device_config": {
                        "supports_uiautomator": True,
                        "supports_appium": True,
                        "supports_screenshot": True,
                        "is_emulator": False
                    }
                }
            ]
            
            # 检查设备是否已存在，如果不存在则插入
            for device_data in mock_devices:
                stmt = select(AndroidDevice).where(AndroidDevice.device_id == device_data["device_id"])
                result = await session.execute(stmt)
                existing_device = result.scalar_one_or_none()

                if not existing_device:
                    device = AndroidDevice(**device_data)
                    session.add(device)
                    print(f"✅ 插入设备: {device_data['device_name']} ({device_data['device_id']})")
                else:
                    print(f"⚠️ 设备已存在: {device_data['device_name']} ({device_data['device_id']})")

            # 提交更改
            await session.commit()
            print(f"\n🎉 模拟设备数据插入完成！")

            # 查询并显示所有设备
            stmt = select(AndroidDevice)
            result = await session.execute(stmt)
            devices = result.scalars().all()
            
            print(f"\n📱 当前数据库中的设备列表 ({len(devices)} 个):")
            for device in devices:
                print(f"  - {device.device_name} ({device.device_id}) - {device.status}")
            
            break  # 只需要一个会话
            
    except Exception as e:
        print(f"❌ 插入模拟设备数据失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始插入模拟Android设备数据...")
    asyncio.run(insert_mock_devices())
