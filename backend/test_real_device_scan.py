#!/usr/bin/env python3
"""
测试真实Android设备扫描功能
"""
import asyncio
import sys
import os
import subprocess

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.agents.android.android_device_manager_agent import AndroidDeviceManagerAgent

async def test_adb_installation():
    """测试ADB安装状态"""
    print("🔍 检查ADB安装状态...")
    
    try:
        # 检查ADB版本
        result = subprocess.run(["adb", "version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ ADB已安装: {result.stdout.strip().split()[0]} {result.stdout.strip().split()[4]}")
            return True
        else:
            print(f"❌ ADB版本检查失败: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ ADB未安装或未在PATH中")
        print("📋 安装说明:")
        print("  1. 下载Android SDK Platform Tools:")
        print("     https://developer.android.com/studio/releases/platform-tools")
        print("  2. 解压到任意目录")
        print("  3. 将解压目录添加到系统PATH环境变量")
        print("  4. 重新启动命令行工具")
        return False
    except Exception as e:
        print(f"❌ 检查ADB失败: {e}")
        return False

async def test_device_detection():
    """测试设备检测"""
    print("\n📱 检查连接的Android设备...")
    
    try:
        # 直接使用ADB命令检查设备
        result = subprocess.run(["adb", "devices", "-l"], capture_output=True, text=True)
        if result.returncode == 0:
            output = result.stdout.strip()
            print(f"ADB输出:\n{output}")
            
            lines = output.split('\n')[1:]  # 跳过标题行
            devices = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                if '\tdevice' in line:
                    device_id = line.split('\t')[0]
                    devices.append(device_id)
                    print(f"✅ 发现设备: {device_id}")
                elif '\tunauthorized' in line:
                    device_id = line.split('\t')[0]
                    print(f"⚠️ 设备未授权: {device_id}")
                    print("   请在设备上确认USB调试授权")
                elif '\toffline' in line:
                    device_id = line.split('\t')[0]
                    print(f"⚠️ 设备离线: {device_id}")
            
            if not devices:
                print("❌ 未发现可用设备")
                print("📋 检查清单:")
                print("  1. Android设备已通过USB连接到电脑")
                print("  2. 设备已开启开发者选项")
                print("  3. 设备已开启USB调试")
                print("  4. 设备已授权此电脑进行调试")
                print("  5. 尝试重新连接USB线缆")
            
            return devices
        else:
            print(f"❌ ADB devices命令失败: {result.stderr}")
            return []
    except Exception as e:
        print(f"❌ 设备检测失败: {e}")
        return []

async def test_device_manager_agent():
    """测试设备管理智能体"""
    print("\n🤖 测试设备管理智能体...")
    
    try:
        agent = AndroidDeviceManagerAgent()
        devices = await agent.scan_devices()
        
        print(f"📱 智能体扫描结果: 发现 {len(devices)} 个设备")
        
        for device in devices:
            print(f"  - {device['device_name']} ({device['device_id']})")
            print(f"    制造商: {device['manufacturer']}")
            print(f"    Android版本: {device['android_version']}")
            print(f"    屏幕分辨率: {device['screen_resolution']}")
            print(f"    连接类型: {device['connection_type']}")
        
        return devices
    except Exception as e:
        print(f"❌ 智能体测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

async def test_database_integration():
    """测试数据库集成"""
    print("\n💾 测试数据库集成...")
    
    try:
        from app.database.connection import get_database
        from app.database.models.android_analysis import AndroidDevice
        from sqlalchemy import select
        
        async for session in get_database():
            # 查询数据库中的设备
            stmt = select(AndroidDevice)
            result = await session.execute(stmt)
            devices = result.scalars().all()
            
            print(f"📊 数据库中的设备数量: {len(devices)}")
            
            for device in devices:
                print(f"  - {device.device_name} ({device.device_id})")
                print(f"    状态: {device.status}")
                print(f"    最后见到: {device.last_seen}")
            
            break  # 只需要一个会话
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("🚀 Android设备扫描功能测试")
    print("=" * 50)
    
    # 1. 检查ADB安装
    adb_available = await test_adb_installation()
    
    if not adb_available:
        print("\n❌ ADB未正确安装，无法继续测试")
        return
    
    # 2. 检查设备连接
    devices = await test_device_detection()
    
    # 3. 测试智能体
    agent_devices = await test_device_manager_agent()
    
    # 4. 测试数据库集成
    await test_database_integration()
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试总结")
    print("=" * 50)
    print(f"ADB状态: {'✅ 可用' if adb_available else '❌ 不可用'}")
    print(f"检测到的设备: {len(devices)} 个")
    print(f"智能体扫描: {len(agent_devices)} 个")
    
    if devices:
        print("\n✅ 设备扫描功能正常工作")
        print("💡 建议: 访问 http://localhost:3001/android/devices 查看设备列表")
    else:
        print("\n⚠️ 未检测到设备，请检查设备连接和USB调试设置")

if __name__ == "__main__":
    asyncio.run(main())
