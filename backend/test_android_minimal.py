#!/usr/bin/env python3
"""
最小化Android模块测试（只测试核心功能）
"""
import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

def test_android_minimal():
    """最小化Android模块测试"""
    try:
        print("🚀 最小化Android模块测试...")
        
        # 1. 测试消息类型
        print("\n📋 1. 测试Android消息类型...")
        from app.core.messages.android import (
            AndroidAnalysisRequest,
            AndroidAnalysisResult,
            AndroidUIElement,
            AndroidTestStep,
            AndroidScriptGenerationMessage,
            AndroidGeneratedScript,
            AndroidScriptExecutionMessage,
            AndroidDeviceConnectionMessage
        )
        print("✅ 所有Android消息类型导入成功")
        
        # 2. 测试类型定义
        print("\n🏷️ 2. 测试Android类型定义...")
        from app.core.types.enums import AgentTypes, TopicTypes
        from app.core.types.constants import AGENT_NAMES
        
        android_agents = [
            AgentTypes.ANDROID_ANALYZER,
            AgentTypes.ANDROID_SCRIPT_GENERATOR,
            AgentTypes.ANDROID_EXECUTOR,
            AgentTypes.ANDROID_DEVICE_MANAGER,
            AgentTypes.ANDROID_DATA_STORAGE
        ]
        
        for agent_type in android_agents:
            agent_name = AGENT_NAMES.get(agent_type.value, '未定义')
            print(f"✅ {agent_type.value}: {agent_name}")
        
        # 3. 测试消息对象创建
        print("\n📨 3. 测试Android消息对象创建...")
        
        # 创建UI元素
        ui_element = AndroidUIElement(
            element_type="Button",
            description="登录按钮",
            resource_id="com.example.app:id/btn_login",
            text="登录",
            clickable=True,
            scrollable=False,
            enabled=True,
            bounds={"left": 100, "top": 200, "right": 300, "bottom": 250},
            confidence_score=0.95
        )
        print(f"✅ UI元素创建成功: {ui_element.description}")
        
        # 创建测试步骤
        test_step = AndroidTestStep(
            step_number=1,
            action="click",
            target="登录按钮",
            description="点击登录按钮",
            expected_result="进入登录页面",
            selector="resource-id('com.example.app:id/btn_login')",
            coordinates={"x": 200, "y": 225}
        )
        print(f"✅ 测试步骤创建成功: {test_step.description}")
        
        # 创建分析请求
        analysis_request = AndroidAnalysisRequest(
            session_id="test_session_123",
            screenshot_data="base64_encoded_screenshot_data",
            test_description="测试Android应用登录功能",
            ui_hierarchy="<hierarchy><node class='android.widget.Button' resource-id='com.example.app:id/btn_login' text='登录'/></hierarchy>",
            additional_context="这是一个登录页面测试",
            device_info={
                "device_id": "emulator-5554",
                "platform_version": "11.0",
                "screen_resolution": "1080x1920"
            }
        )
        print(f"✅ 分析请求创建成功: {analysis_request.session_id}")
        
        # 创建分析结果
        analysis_result = AndroidAnalysisResult(
            ui_elements=[ui_element],
            test_scenarios=["用户登录流程测试", "输入验证测试"],
            test_steps=[test_step],
            analysis_summary="这是一个登录页面，包含用户名输入框、密码输入框和登录按钮",
            confidence_score=0.95,
            activity_name="LoginActivity",
            package_name="com.example.app",
            screen_resolution={"width": 1080, "height": 1920}
        )
        print(f"✅ 分析结果创建成功: 包含{len(analysis_result.ui_elements)}个UI元素")
        
        # 创建脚本生成消息
        script_message = AndroidScriptGenerationMessage(
            session_id="test_session_123",
            requirement_id="req_123",
            analysis_result=analysis_result.model_dump(),
            test_description="生成Android登录测试脚本",
            generate_formats=["midscene", "appium", "uiautomator2"],
            additional_context="优先生成MidScene.js格式的脚本"
        )
        print(f"✅ 脚本生成消息创建成功: {script_message.session_id}")
        
        # 创建生成的脚本
        generated_script = AndroidGeneratedScript(
            format="midscene",
            content="""
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';

async function testLogin() {
    const devices = await getConnectedDevices();
    const page = new AndroidDevice(devices[0].udid);
    const agent = new AndroidAgent(page);
    
    await page.connect();
    await page.launch('com.example.app');
    
    await agent.aiAction('点击登录按钮');
    await agent.aiAction('输入用户名和密码');
    await agent.aiAssert('登录成功');
    
    await page.disconnect();
}
""",
            estimated_duration="3-8分钟",
            script_type="automation",
            framework="midscene",
            file_path="android_test_midscene.ts"
        )
        print(f"✅ 生成脚本创建成功: {generated_script.format}格式")
        
        # 创建脚本执行消息
        execution_message = AndroidScriptExecutionMessage(
            session_id="test_session_123",
            execution_id="exec_456",
            script_type="midscene",
            script_content=generated_script.content,
            execution_config={"timeout": 300, "retry_count": 3},
            device_config={
                "device_id": "emulator-5554",
                "platform_version": "11.0",
                "app_package": "com.example.app"
            }
        )
        print(f"✅ 执行消息创建成功: {execution_message.execution_id}")
        
        # 创建设备连接消息
        device_message = AndroidDeviceConnectionMessage(
            session_id="test_session_123",
            device_id="emulator-5554",
            connection_type="adb",
            device_capabilities={"automation": "UiAutomator2"},
            device_config={
                "platform_name": "Android",
                "platform_version": "11.0",
                "device_name": "Android Emulator"
            },
            app_package="com.example.app",
            app_activity=".LoginActivity"
        )
        print(f"✅ 设备连接消息创建成功: {device_message.device_id}")
        
        # 4. 测试消息序列化和反序列化
        print("\n🔄 4. 测试消息序列化和反序列化...")
        
        # 测试UI元素序列化
        ui_element_dict = ui_element.model_dump()
        ui_element_restored = AndroidUIElement(**ui_element_dict)
        assert ui_element_restored.description == ui_element.description
        print("✅ UI元素序列化/反序列化成功")
        
        # 测试分析结果序列化
        analysis_dict = analysis_result.model_dump()
        analysis_restored = AndroidAnalysisResult(**analysis_dict)
        assert len(analysis_restored.ui_elements) == len(analysis_result.ui_elements)
        print("✅ 分析结果序列化/反序列化成功")
        
        # 测试脚本消息序列化
        script_dict = script_message.model_dump()
        script_restored = AndroidScriptGenerationMessage(**script_dict)
        assert script_restored.session_id == script_message.session_id
        print("✅ 脚本消息序列化/反序列化成功")
        
        # 5. 测试数据验证
        print("\n✅ 5. 测试数据验证...")
        
        # 验证必填字段
        try:
            AndroidUIElement(element_type="", description="")  # 应该失败
            print("❌ 数据验证失败")
        except Exception:
            print("✅ 必填字段验证正常")
        
        # 验证枚举值
        assert ui_element.element_type == "Button"
        assert test_step.action == "click"
        print("✅ 枚举值验证正常")
        
        # 验证嵌套对象
        assert isinstance(analysis_result.ui_elements[0], AndroidUIElement)
        assert isinstance(analysis_result.test_steps[0], AndroidTestStep)
        print("✅ 嵌套对象验证正常")
        
        print("\n🎉 Android模块最小化测试完全通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_android_types_coverage():
    """测试Android类型覆盖度"""
    try:
        print("\n📊 测试Android类型覆盖度...")
        
        from app.core.types.enums import AgentTypes, TopicTypes
        from app.core.types.constants import AGENT_NAMES
        
        # 检查所有Android智能体类型
        android_agent_types = [attr for attr in dir(AgentTypes) if attr.startswith('ANDROID_')]
        android_topic_types = [attr for attr in dir(TopicTypes) if attr.startswith('ANDROID_')]
        
        print(f"✅ 发现 {len(android_agent_types)} 个Android智能体类型:")
        for agent_type in android_agent_types:
            agent_enum = getattr(AgentTypes, agent_type)
            agent_name = AGENT_NAMES.get(agent_enum.value, '未定义')
            print(f"   - {agent_type}: {agent_enum.value} ({agent_name})")
        
        print(f"✅ 发现 {len(android_topic_types)} 个Android主题类型:")
        for topic_type in android_topic_types:
            topic_enum = getattr(TopicTypes, topic_type)
            print(f"   - {topic_type}: {topic_enum.value}")
        
        # 检查类型一致性
        agent_values = [getattr(AgentTypes, attr).value for attr in android_agent_types]
        topic_values = [getattr(TopicTypes, attr).value for attr in android_topic_types]
        
        if set(agent_values) == set(topic_values):
            print("✅ 智能体类型和主题类型一致")
        else:
            print("❌ 智能体类型和主题类型不一致")
            print(f"   智能体独有: {set(agent_values) - set(topic_values)}")
            print(f"   主题独有: {set(topic_values) - set(agent_values)}")
        
        # 检查名称映射覆盖度
        missing_names = [value for value in agent_values if value not in AGENT_NAMES]
        if missing_names:
            print(f"❌ 缺少名称映射: {missing_names}")
        else:
            print("✅ 所有智能体都有名称映射")
        
        return True
        
    except Exception as e:
        print(f"❌ 类型覆盖度测试失败: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 开始Android模块最小化测试...")
    
    success1 = test_android_minimal()
    success2 = test_android_types_coverage()
    
    if success1 and success2:
        print("\n🎉 🎉 🎉 Android模块最小化测试完全通过！")
        print("\n📋 测试结果总结:")
        print("   ✅ 消息类型系统完整且正常")
        print("   ✅ 类型定义完整且一致")
        print("   ✅ 消息对象创建和验证正常")
        print("   ✅ 序列化/反序列化正常")
        print("   ✅ 数据验证机制正常")
        print("   ✅ 类型覆盖度完整")
        print("\n🎯 Android自动化测试系统核心已验证！")
        print("\n💡 系统特性:")
        print("   - 支持5种Android智能体类型")
        print("   - 完整的消息传递机制")
        print("   - 强类型数据验证")
        print("   - 优先支持MidScene.js框架")
        print("   - 兼容传统自动化框架")
        print("\n🚀 可以开始集成和使用Android自动化功能！")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
