#!/usr/bin/env python3
"""
路由验证脚本
验证所有Android相关的前端路由和后端API是否正常工作
"""
import asyncio
import aiohttp
import json
from typing import List, Dict, Any

# 测试配置
FRONTEND_BASE_URL = "http://localhost:3001"
BACKEND_BASE_URL = "http://localhost:8000"

# 前端路由列表
FRONTEND_ROUTES = [
    {"path": "/", "name": "系统主页", "status": "implemented"},
    {"path": "/android", "name": "Android主页", "status": "implemented"},
    {"path": "/android/analysis", "name": "Android界面分析", "status": "implemented"},
    {"path": "/android/devices", "name": "Android设备管理", "status": "implemented"},
    {"path": "/android/create", "name": "Android测试创建", "status": "placeholder"},
    {"path": "/android/scripts", "name": "Android脚本管理", "status": "placeholder"},
    {"path": "/android/execution", "name": "Android脚本执行", "status": "placeholder"},
    {"path": "/android/results", "name": "Android测试结果", "status": "placeholder"},
    {"path": "/android/history", "name": "Android执行历史", "status": "placeholder"},
    {"path": "/android/reports", "name": "Android测试报告", "status": "placeholder"},
]

# 后端API端点列表
BACKEND_APIS = [
    {"path": "/api/v1/system/health", "method": "GET", "name": "系统健康检查"},
    {"path": "/api/v1/android/devices/scan", "method": "GET", "name": "扫描Android设备"},
    {"path": "/api/v1/android/devices/list", "method": "GET", "name": "获取设备列表"},
    {"path": "/api/v1/android/devices/online", "method": "GET", "name": "获取在线设备"},
    {"path": "/api/v1/android/scripts/list", "method": "GET", "name": "获取脚本列表"},
    {"path": "/api/v1/android/execution/history", "method": "GET", "name": "获取执行历史"},
    {"path": "/docs", "method": "GET", "name": "API文档"},
]

async def test_frontend_routes():
    """测试前端路由"""
    print("🔍 测试前端路由...")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        results = []
        
        for route in FRONTEND_ROUTES:
            try:
                url = f"{FRONTEND_BASE_URL}{route['path']}"
                async with session.get(url, timeout=5) as response:
                    status_icon = "✅" if response.status == 200 else "❌"
                    status_text = "正常" if response.status == 200 else f"错误({response.status})"
                    
                    print(f"  {status_icon} {route['name']:<20} | {route['status']:<12} | {status_text}")
                    
                    results.append({
                        "route": route,
                        "status": response.status,
                        "success": response.status == 200
                    })
                    
            except Exception as e:
                print(f"  ❌ {route['name']:<20} | {route['status']:<12} | 连接失败: {str(e)[:30]}")
                results.append({
                    "route": route,
                    "status": 0,
                    "success": False,
                    "error": str(e)
                })
        
        return results

async def test_backend_apis():
    """测试后端API"""
    print("\n🔍 测试后端API...")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        results = []
        
        for api in BACKEND_APIS:
            try:
                url = f"{BACKEND_BASE_URL}{api['path']}"
                
                if api['method'] == 'GET':
                    async with session.get(url, timeout=10) as response:
                        status_icon = "✅" if response.status == 200 else "❌"
                        status_text = "正常" if response.status == 200 else f"错误({response.status})"
                        
                        # 尝试解析JSON响应
                        try:
                            data = await response.json()
                            if isinstance(data, dict) and data.get('success'):
                                status_text += " (成功)"
                        except:
                            pass
                        
                        print(f"  {status_icon} {api['name']:<20} | {api['method']:<6} | {status_text}")
                        
                        results.append({
                            "api": api,
                            "status": response.status,
                            "success": response.status == 200
                        })
                        
            except Exception as e:
                print(f"  ❌ {api['name']:<20} | {api['method']:<6} | 连接失败: {str(e)[:30]}")
                results.append({
                    "api": api,
                    "status": 0,
                    "success": False,
                    "error": str(e)
                })
        
        return results

async def test_android_specific_apis():
    """测试Android特定的API功能"""
    print("\n🔍 测试Android特定功能...")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        # 测试设备扫描
        try:
            async with session.get(f"{BACKEND_BASE_URL}/api/v1/android/devices/scan", timeout=15) as response:
                if response.status == 200:
                    data = await response.json()
                    devices = data.get('data', [])
                    print(f"  ✅ 设备扫描功能正常 | 发现 {len(devices)} 个设备")
                else:
                    print(f"  ❌ 设备扫描功能异常 | 状态码: {response.status}")
        except Exception as e:
            print(f"  ❌ 设备扫描功能异常 | 错误: {str(e)[:50]}")
        
        # 测试脚本列表
        try:
            async with session.get(f"{BACKEND_BASE_URL}/api/v1/android/scripts/list", timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    scripts = data.get('data', [])
                    print(f"  ✅ 脚本管理功能正常 | 脚本数量: {len(scripts)}")
                else:
                    print(f"  ❌ 脚本管理功能异常 | 状态码: {response.status}")
        except Exception as e:
            print(f"  ❌ 脚本管理功能异常 | 错误: {str(e)[:50]}")

def print_summary(frontend_results: List[Dict], backend_results: List[Dict]):
    """打印测试总结"""
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    # 前端路由总结
    frontend_success = sum(1 for r in frontend_results if r['success'])
    frontend_total = len(frontend_results)
    print(f"🎨 前端路由: {frontend_success}/{frontend_total} 正常")
    
    # 后端API总结
    backend_success = sum(1 for r in backend_results if r['success'])
    backend_total = len(backend_results)
    print(f"🔧 后端API: {backend_success}/{backend_total} 正常")
    
    # 整体状态
    total_success = frontend_success + backend_success
    total_tests = frontend_total + backend_total
    success_rate = (total_success / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"📈 整体成功率: {success_rate:.1f}% ({total_success}/{total_tests})")
    
    # 状态判断
    if success_rate >= 90:
        print("🎉 系统状态: 优秀 - 所有主要功能正常")
    elif success_rate >= 70:
        print("✅ 系统状态: 良好 - 大部分功能正常")
    elif success_rate >= 50:
        print("⚠️ 系统状态: 一般 - 部分功能需要检查")
    else:
        print("❌ 系统状态: 异常 - 需要立即修复")
    
    print("\n💡 建议:")
    print("  - 访问 http://localhost:3001 查看前端界面")
    print("  - 访问 http://localhost:8000/docs 查看API文档")
    print("  - 访问 http://localhost:3001/android 体验Android功能")

async def main():
    """主测试函数"""
    print("🚀 Android自动化测试平台 - 路由验证")
    print("=" * 60)
    print(f"前端地址: {FRONTEND_BASE_URL}")
    print(f"后端地址: {BACKEND_BASE_URL}")
    print()
    
    # 测试前端路由
    frontend_results = await test_frontend_routes()
    
    # 测试后端API
    backend_results = await test_backend_apis()
    
    # 测试Android特定功能
    await test_android_specific_apis()
    
    # 打印总结
    print_summary(frontend_results, backend_results)

if __name__ == "__main__":
    asyncio.run(main())
