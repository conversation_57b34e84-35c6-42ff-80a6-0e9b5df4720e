#!/usr/bin/env python3
"""
简化的Android模块导入测试
"""

def test_basic_imports():
    """测试基本的Android模块导入"""
    try:
        print("🔍 测试Android消息类型导入...")
        from app.core.messages.android import (
            AndroidAnalysisRequest,
            AndroidAnalysisResult,
            AndroidUIElement,
            AndroidTestStep,
            AndroidScriptGenerationMessage,
            AndroidGeneratedScript
        )
        print("✅ Android消息类型导入成功")
        
        print("\n🔍 测试Android类型定义导入...")
        from app.core.types import AgentTypes, TopicTypes, AGENT_NAMES
        
        # 检查Android相关的类型是否存在
        android_types = [
            'ANDROID_ANALYZER',
            'ANDROID_SCRIPT_GENERATOR', 
            'ANDROID_EXECUTOR',
            'ANDROID_DEVICE_MANAGER',
            'ANDROID_DATA_STORAGE'
        ]
        
        for type_name in android_types:
            if hasattr(AgentTypes, type_name):
                agent_type = getattr(AgentTypes, type_name)
                print(f"✅ AgentTypes.{type_name}: {agent_type.value}")
                
                if agent_type.value in AGENT_NAMES:
                    print(f"   名称: {AGENT_NAMES[agent_type.value]}")
                else:
                    print(f"   ❌ 缺少名称映射")
            else:
                print(f"❌ 缺少类型定义: AgentTypes.{type_name}")
        
        print("\n🔍 测试Android智能体类导入...")
        try:
            from app.agents.android.android_analyzer_agent import AndroidAnalyzerAgent
            print("✅ AndroidAnalyzerAgent 导入成功")
        except Exception as e:
            print(f"❌ AndroidAnalyzerAgent 导入失败: {str(e)}")
            
        try:
            from app.agents.android.android_script_generator_agent import AndroidScriptGeneratorAgent
            print("✅ AndroidScriptGeneratorAgent 导入成功")
        except Exception as e:
            print(f"❌ AndroidScriptGeneratorAgent 导入失败: {str(e)}")
            
        try:
            from app.agents.android.android_executor_agent import AndroidExecutorAgent
            print("✅ AndroidExecutorAgent 导入成功")
        except Exception as e:
            print(f"❌ AndroidExecutorAgent 导入失败: {str(e)}")
            
        try:
            from app.agents.android.android_device_manager_agent import AndroidDeviceManagerAgent
            print("✅ AndroidDeviceManagerAgent 导入成功")
        except Exception as e:
            print(f"❌ AndroidDeviceManagerAgent 导入失败: {str(e)}")
            
        try:
            from app.agents.android.android_data_storage_agent import AndroidDataStorageAgent
            print("✅ AndroidDataStorageAgent 导入成功")
        except Exception as e:
            print(f"❌ AndroidDataStorageAgent 导入失败: {str(e)}")
        
        print("\n🔍 测试Android模块整体导入...")
        try:
            from app.agents.android import (
                AndroidAnalyzerAgent,
                AndroidScriptGeneratorAgent,
                AndroidExecutorAgent,
                AndroidDeviceManagerAgent,
                AndroidDataStorageAgent
            )
            print("✅ Android模块整体导入成功")
        except Exception as e:
            print(f"❌ Android模块整体导入失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_message_creation():
    """测试消息对象创建"""
    try:
        print("\n🔍 测试Android消息对象创建...")
        
        from app.core.messages.android import (
            AndroidAnalysisRequest,
            AndroidUIElement,
            AndroidTestStep,
            AndroidScriptGenerationMessage
        )
        
        # 测试创建UI元素
        ui_element = AndroidUIElement(
            element_type="Button",
            description="登录按钮",
            resource_id="com.example.app:id/btn_login",
            text="登录",
            clickable=True
        )
        print(f"✅ UI元素创建成功: {ui_element.description}")
        
        # 测试创建分析请求
        analysis_request = AndroidAnalysisRequest(
            session_id="test_session_123",
            screenshot_data="base64_encoded_data",
            test_description="测试登录功能"
        )
        print(f"✅ 分析请求创建成功: {analysis_request.session_id}")
        
        # 测试创建脚本生成消息
        script_message = AndroidScriptGenerationMessage(
            session_id="test_session_123",
            requirement_id="req_123",
            analysis_result={"ui_elements": [], "test_scenarios": []},
            test_description="生成登录测试脚本"
        )
        print(f"✅ 脚本生成消息创建成功: {script_message.session_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 消息对象创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始Android模块简化测试...")
    
    success1 = test_basic_imports()
    success2 = test_message_creation()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！Android模块导入正常。")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
