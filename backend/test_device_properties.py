#!/usr/bin/env python3
"""
测试获取Android设备属性
"""
import asyncio
import subprocess
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_device_properties():
    """测试获取设备属性"""
    print("🔍 测试Android设备属性获取...")
    
    # 首先获取设备列表
    try:
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ 无法获取设备列表")
            return
        
        lines = result.stdout.strip().split('\n')[1:]
        devices = []
        for line in lines:
            if line.strip() and 'device' in line:
                device_id = line.split()[0]
                devices.append(device_id)
        
        if not devices:
            print("❌ 未找到连接的设备")
            return
        
        device_id = devices[0]
        print(f"📱 测试设备: {device_id}")
        
        # 获取关键属性
        key_props = [
            "ro.product.model",
            "ro.product.manufacturer", 
            "ro.product.brand",
            "ro.product.name",
            "ro.product.device",
            "ro.build.version.release",
            "ro.build.version.sdk",
            "ro.build.display.id"
        ]
        
        print("\n📋 关键属性:")
        for prop in key_props:
            try:
                result = subprocess.run(
                    ["adb", "-s", device_id, "shell", "getprop", prop], 
                    capture_output=True, text=True, timeout=5
                )
                if result.returncode == 0:
                    value = result.stdout.strip()
                    print(f"  {prop}: {value}")
                else:
                    print(f"  {prop}: 获取失败")
            except Exception as e:
                print(f"  {prop}: 错误 - {e}")
        
        # 获取所有属性（前20行）
        print("\n📄 所有属性（前20行）:")
        try:
            result = subprocess.run(
                ["adb", "-s", device_id, "shell", "getprop"], 
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[:20]
                for i, line in enumerate(lines, 1):
                    print(f"  {i:2d}: {line}")
            else:
                print("  获取所有属性失败")
        except Exception as e:
            print(f"  获取所有属性错误: {e}")
        
        # 测试设备名称组合逻辑
        print("\n🏷️ 设备名称组合测试:")
        try:
            # 获取品牌和型号
            brand_result = subprocess.run(
                ["adb", "-s", device_id, "shell", "getprop", "ro.product.brand"], 
                capture_output=True, text=True, timeout=5
            )
            model_result = subprocess.run(
                ["adb", "-s", device_id, "shell", "getprop", "ro.product.model"], 
                capture_output=True, text=True, timeout=5
            )
            manufacturer_result = subprocess.run(
                ["adb", "-s", device_id, "shell", "getprop", "ro.product.manufacturer"], 
                capture_output=True, text=True, timeout=5
            )
            
            brand = brand_result.stdout.strip() if brand_result.returncode == 0 else ""
            model = model_result.stdout.strip() if model_result.returncode == 0 else ""
            manufacturer = manufacturer_result.stdout.strip() if manufacturer_result.returncode == 0 else ""
            
            print(f"  品牌 (brand): '{brand}'")
            print(f"  型号 (model): '{model}'")
            print(f"  制造商 (manufacturer): '{manufacturer}'")
            
            # 生成设备名称
            if brand and model:
                if brand.lower() not in model.lower():
                    device_name = f"{brand} {model}"
                else:
                    device_name = model
            elif manufacturer and model:
                if manufacturer.lower() not in model.lower():
                    device_name = f"{manufacturer} {model}"
                else:
                    device_name = model
            elif model:
                device_name = model
            elif brand:
                device_name = f"{brand} Device"
            elif manufacturer:
                device_name = f"{manufacturer} Device"
            else:
                device_name = f"Android Device {device_id[:8]}"
            
            print(f"  建议设备名称: '{device_name}'")
            
        except Exception as e:
            print(f"  设备名称测试失败: {e}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_device_properties())
