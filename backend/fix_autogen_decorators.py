#!/usr/bin/env python3
"""
批量移除所有智能体的AutoGen装饰器
"""
import os
import re
import glob

def fix_agent_file(file_path):
    """修复单个智能体文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 移除 @type_subscription 装饰器
        content = re.sub(r'@type_subscription\([^)]+\)\s*\n', '', content)
        
        # 移除 type_subscription 导入
        content = re.sub(r'from autogen_core import[^,\n]*type_subscription[^,\n]*,?\s*', '', content)
        content = re.sub(r',\s*type_subscription', '', content)
        content = re.sub(r'type_subscription\s*,?\s*', '', content)
        
        # 清理空的导入行
        content = re.sub(r'from autogen_core import\s*\n', '', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复: {file_path}")
            return True
        else:
            print(f"⚪ 跳过: {file_path} (无需修改)")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {file_path} - {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复AutoGen装饰器...")
    
    # 查找所有智能体文件
    agent_files = []
    
    # Web智能体
    web_agents = glob.glob("app/agents/web/*_agent.py")
    agent_files.extend(web_agents)
    
    # Android智能体
    android_agents = glob.glob("app/agents/android/*_agent.py")
    agent_files.extend(android_agents)
    
    print(f"📁 找到 {len(agent_files)} 个智能体文件")
    
    fixed_count = 0
    for file_path in agent_files:
        if fix_agent_file(file_path):
            fixed_count += 1
    
    print(f"\n🎉 修复完成: {fixed_count}/{len(agent_files)} 个文件已修复")

if __name__ == "__main__":
    main()
