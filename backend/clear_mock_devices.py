#!/usr/bin/env python3
"""
清理模拟Android设备数据
"""
import asyncio
import sys
import os
from sqlalchemy import select, delete

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.connection import get_database
from app.database.models.android_analysis import AndroidDevice

async def clear_mock_devices():
    """清理模拟设备数据"""
    try:
        # 获取数据库会话
        async for session in get_database():
            # 查询所有设备
            stmt = select(AndroidDevice)
            result = await session.execute(stmt)
            devices = result.scalars().all()
            
            print(f"📱 当前数据库中的设备数量: {len(devices)}")
            for device in devices:
                print(f"  - {device.device_name} ({device.device_id}) - {device.status}")
            
            if devices:
                # 删除所有设备
                delete_stmt = delete(AndroidDevice)
                await session.execute(delete_stmt)
                await session.commit()
                print(f"\n🗑️ 已清理 {len(devices)} 个模拟设备")
            else:
                print("\n✅ 数据库中没有设备数据")
            
            break  # 只需要一个会话
            
    except Exception as e:
        print(f"❌ 清理模拟设备数据失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧹 开始清理模拟Android设备数据...")
    asyncio.run(clear_mock_devices())
