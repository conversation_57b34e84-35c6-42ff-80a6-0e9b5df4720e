#!/usr/bin/env python3
"""
测试Android智能体模块的导入
"""

def test_android_imports():
    """测试Android相关模块的导入"""
    try:
        print("🔍 测试Android消息类型导入...")
        from app.core.messages.android import (
            AndroidAnalysisRequest,
            AndroidAnalysisResult,
            AndroidUIElement,
            AndroidTestStep,
            AndroidScriptGenerationMessage,
            AndroidGeneratedScript,
            AndroidScriptExecutionMessage,
            AndroidDeviceConnectionMessage
        )
        print("✅ Android消息类型导入成功")
        
        print("\n🔍 测试Android智能体导入...")
        from app.agents.android import (
            AndroidAnalyzerAgent,
            AndroidScriptGeneratorAgent,
            AndroidExecutorAgent,
            AndroidDeviceManagerAgent,
            AndroidDataStorageAgent
        )
        print("✅ Android智能体导入成功")
        
        print("\n🔍 测试Android类型定义导入...")
        from app.core.types import AgentTypes, TopicTypes, AGENT_NAMES
        
        # 检查Android相关的类型是否存在
        android_agent_types = [
            AgentTypes.ANDROID_ANALYZER,
            AgentTypes.ANDROID_SCRIPT_GENERATOR,
            AgentTypes.ANDROID_EXECUTOR,
            AgentTypes.ANDROID_DEVICE_MANAGER,
            AgentTypes.ANDROID_DATA_STORAGE
        ]
        
        android_topic_types = [
            TopicTypes.ANDROID_ANALYZER,
            TopicTypes.ANDROID_SCRIPT_GENERATOR,
            TopicTypes.ANDROID_EXECUTOR,
            TopicTypes.ANDROID_DEVICE_MANAGER,
            TopicTypes.ANDROID_DATA_STORAGE
        ]
        
        for agent_type in android_agent_types:
            if agent_type.value in AGENT_NAMES:
                print(f"✅ {agent_type.value}: {AGENT_NAMES[agent_type.value]}")
            else:
                print(f"❌ 缺少智能体名称映射: {agent_type.value}")
        
        print("✅ Android类型定义导入成功")
        
        print("\n🔍 测试智能体工厂导入...")
        try:
            from app.agents.factory import agent_factory
            print("✅ 智能体工厂导入成功")

            # 检查Android智能体是否在工厂中注册
            factory_agents = agent_factory._agent_classes
            for agent_type in android_agent_types:
                if agent_type.value in factory_agents:
                    print(f"✅ {agent_type.value} 已在工厂中注册")
                else:
                    print(f"❌ {agent_type.value} 未在工厂中注册")

        except Exception as e:
            print(f"❌ 智能体工厂测试失败: {str(e)}")

        print("✅ 智能体工厂测试完成")
        
        print("\n🎉 所有Android模块导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


def test_android_message_creation():
    """测试Android消息对象创建"""
    try:
        print("\n🔍 测试Android消息对象创建...")
        
        from app.core.messages.android import (
            AndroidAnalysisRequest,
            AndroidUIElement,
            AndroidTestStep,
            AndroidScriptGenerationMessage
        )
        
        # 测试创建UI元素
        ui_element = AndroidUIElement(
            element_type="Button",
            description="登录按钮",
            resource_id="com.example.app:id/btn_login",
            text="登录",
            clickable=True
        )
        print(f"✅ UI元素创建成功: {ui_element.description}")
        
        # 测试创建测试步骤
        test_step = AndroidTestStep(
            step_number=1,
            action="click",
            target="登录按钮",
            description="点击登录按钮",
            expected_result="进入登录页面"
        )
        print(f"✅ 测试步骤创建成功: {test_step.description}")
        
        # 测试创建分析请求
        analysis_request = AndroidAnalysisRequest(
            session_id="test_session_123",
            screenshot_data="base64_encoded_data",
            test_description="测试登录功能"
        )
        print(f"✅ 分析请求创建成功: {analysis_request.session_id}")
        
        # 测试创建脚本生成消息
        script_message = AndroidScriptGenerationMessage(
            session_id="test_session_123",
            requirement_id="req_123",
            analysis_result={"ui_elements": [], "test_scenarios": []},
            test_description="生成登录测试脚本",
            generate_formats=["midscene", "appium"]
        )
        print(f"✅ 脚本生成消息创建成功: {script_message.session_id}")
        
        print("✅ Android消息对象创建测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 消息对象创建测试失败: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 开始Android模块导入测试...")
    
    success1 = test_android_imports()
    success2 = test_android_message_creation()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！Android模块已准备就绪。")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
