#!/usr/bin/env python3
"""
Android API测试脚本
用于测试Android相关的API端点是否正常工作
"""
import asyncio
import aiohttp
import json
import base64
from pathlib import Path

# 测试配置
BASE_URL = "http://localhost:8000/api/v1"
TEST_SESSION_ID = "test_android_session_001"

async def test_android_devices_api():
    """测试Android设备API"""
    print("🔍 测试Android设备API...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试设备扫描
            print("  - 测试设备扫描...")
            async with session.get(f"{BASE_URL}/android/devices/scan") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"    ✅ 设备扫描成功: {data.get('message', '')}")
                    devices = data.get('data', [])
                    print(f"    📱 发现 {len(devices)} 个设备")
                    for device in devices:
                        print(f"      - {device.get('device_id', 'Unknown')}: {device.get('status', 'Unknown')}")
                else:
                    print(f"    ❌ 设备扫描失败: {response.status}")
            
            # 测试设备列表
            print("  - 测试设备列表...")
            async with session.get(f"{BASE_URL}/android/devices/list") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"    ✅ 获取设备列表成功")
                    devices = data.get('data', [])
                    print(f"    📱 设备总数: {len(devices)}")
                else:
                    print(f"    ❌ 获取设备列表失败: {response.status}")
            
            # 测试在线设备
            print("  - 测试在线设备...")
            async with session.get(f"{BASE_URL}/android/devices/online") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"    ✅ 获取在线设备成功")
                    devices = data.get('data', [])
                    print(f"    📱 在线设备: {len(devices)}")
                    return devices
                else:
                    print(f"    ❌ 获取在线设备失败: {response.status}")
                    return []
                    
        except Exception as e:
            print(f"    ❌ 设备API测试失败: {e}")
            return []

async def test_android_analysis_api():
    """测试Android分析API"""
    print("🔍 测试Android分析API...")
    
    # 创建一个测试用的base64图片数据（1x1像素的PNG）
    test_image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    analysis_request = {
        "session_id": TEST_SESSION_ID,
        "screenshot_data": test_image_data,
        "device_id": "test_device",
        "package_name": "com.example.testapp",
        "activity_name": "MainActivity",
        "test_description": "测试Android界面分析",
        "additional_context": "这是一个测试应用的主界面",
        "generate_formats": ["appium"],
        "include_non_interactive": False
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            print("  - 启动Android界面分析...")
            async with session.post(
                f"{BASE_URL}/android/analysis/analyze",
                json=analysis_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"    ✅ 分析启动成功")
                    session_id = data.get('session_id')
                    print(f"    📝 会话ID: {session_id}")
                    
                    # 测试状态查询
                    print("  - 测试分析状态查询...")
                    async with session.get(f"{BASE_URL}/android/analysis/status/{session_id}") as status_response:
                        if status_response.status == 200:
                            status_data = await status_response.json()
                            print(f"    ✅ 状态查询成功: {status_data.get('data', {}).get('status', 'Unknown')}")
                        else:
                            print(f"    ❌ 状态查询失败: {status_response.status}")
                    
                    return session_id
                else:
                    error_data = await response.json()
                    print(f"    ❌ 分析启动失败: {response.status} - {error_data.get('detail', '')}")
                    return None
                    
        except Exception as e:
            print(f"    ❌ 分析API测试失败: {e}")
            return None

async def test_android_scripts_api():
    """测试Android脚本API"""
    print("🔍 测试Android脚本API...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试脚本列表
            print("  - 测试脚本列表...")
            async with session.get(f"{BASE_URL}/android/scripts/list") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"    ✅ 获取脚本列表成功")
                    scripts = data.get('data', [])
                    print(f"    📝 脚本总数: {len(scripts)}")
                else:
                    print(f"    ❌ 获取脚本列表失败: {response.status}")
                    
        except Exception as e:
            print(f"    ❌ 脚本API测试失败: {e}")

async def test_android_execution_api():
    """测试Android执行API"""
    print("🔍 测试Android执行API...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试执行历史
            print("  - 测试执行历史...")
            async with session.get(f"{BASE_URL}/android/execution/history") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"    ✅ 获取执行历史成功")
                    executions = data.get('data', [])
                    print(f"    📊 执行记录: {len(executions)}")
                else:
                    print(f"    ❌ 获取执行历史失败: {response.status}")
                    
        except Exception as e:
            print(f"    ❌ 执行API测试失败: {e}")

async def test_system_health():
    """测试系统健康状态"""
    print("🔍 测试系统健康状态...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/system/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"    ✅ 系统健康检查通过")
                    print(f"    📊 系统状态: {data.get('status', 'Unknown')}")
                else:
                    print(f"    ❌ 系统健康检查失败: {response.status}")
                    
        except Exception as e:
            print(f"    ❌ 系统健康检查失败: {e}")

async def main():
    """主测试函数"""
    print("🚀 开始Android API测试...")
    print("=" * 50)
    
    # 测试系统健康状态
    await test_system_health()
    print()
    
    # 测试Android设备API
    devices = await test_android_devices_api()
    print()
    
    # 测试Android分析API
    analysis_session = await test_android_analysis_api()
    print()
    
    # 测试Android脚本API
    await test_android_scripts_api()
    print()
    
    # 测试Android执行API
    await test_android_execution_api()
    print()
    
    print("=" * 50)
    print("✅ Android API测试完成!")
    
    # 输出测试总结
    print("\n📊 测试总结:")
    print(f"  - 发现设备: {len(devices)} 个")
    if analysis_session:
        print(f"  - 分析会话: {analysis_session}")
    else:
        print("  - 分析会话: 未创建")
    
    print("\n💡 提示:")
    print("  - 如果设备扫描失败，请确保ADB已安装并在PATH中")
    print("  - 如果分析失败，请检查AI模型配置")
    print("  - 完整功能需要连接真实的Android设备")

if __name__ == "__main__":
    asyncio.run(main())
