"""
UI自动化测试系统 - Android模块消息类型
定义Android平台智能体间通信的消息结构
"""
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

from .base import BaseMessage


# ============ Android模块枚举类型 ============

class AndroidElementType(str, Enum):
    """Android UI元素类型"""
    BUTTON = "Button"
    EDIT_TEXT = "EditText"
    TEXT_VIEW = "TextView"
    IMAGE_VIEW = "ImageView"
    LINEAR_LAYOUT = "LinearLayout"
    RELATIVE_LAYOUT = "RelativeLayout"
    FRAME_LAYOUT = "FrameLayout"
    RECYCLER_VIEW = "RecyclerView"
    LIST_VIEW = "ListView"
    SCROLL_VIEW = "ScrollView"
    TAB_LAYOUT = "TabLayout"
    VIEW_PAGER = "ViewPager"
    TOOLBAR = "Toolbar"
    MENU_ITEM = "MenuItem"
    CHECKBOX = "CheckBox"
    RADIO_BUTTON = "RadioButton"
    SWITCH = "Switch"
    PROGRESS_BAR = "ProgressBar"
    SEEK_BAR = "SeekBar"
    SPINNER = "Spinner"
    WEB_VIEW = "WebView"
    OTHER = "Other"


class AndroidActionType(str, Enum):
    """Android操作类型"""
    CLICK = "click"
    LONG_CLICK = "long_click"
    TYPE = "type"
    CLEAR = "clear"
    SWIPE = "swipe"
    SCROLL = "scroll"
    DRAG = "drag"
    PINCH = "pinch"
    WAIT = "wait"
    ASSERT = "assert"
    SCREENSHOT = "screenshot"
    BACK = "back"
    HOME = "home"
    MENU = "menu"


class AndroidScriptType(str, Enum):
    """Android脚本类型"""
    APPIUM = "appium"
    UIAUTOMATOR2 = "uiautomator2"
    MIDSCENE = "midscene"


# ============ Android模块基础数据模型 ============

class AndroidUIElement(BaseModel):
    """Android UI元素信息"""
    element_type: str = Field(..., description="元素类型: button, edittext, textview等")
    description: str = Field(..., description="元素描述")
    resource_id: Optional[str] = Field(None, description="资源ID")
    text: Optional[str] = Field(None, description="元素文本")
    content_desc: Optional[str] = Field(None, description="内容描述")
    class_name: Optional[str] = Field(None, description="类名")
    package_name: Optional[str] = Field(None, description="包名")
    bounds: Optional[Dict[str, int]] = Field(None, description="元素边界坐标")
    clickable: bool = Field(default=False, description="是否可点击")
    scrollable: bool = Field(default=False, description="是否可滚动")
    enabled: bool = Field(default=True, description="是否启用")
    confidence_score: float = Field(default=0.0, description="AI识别置信度")


class AndroidTestStep(BaseModel):
    """Android测试步骤"""
    step_number: int = Field(..., description="步骤序号")
    action: str = Field(..., description="操作类型: click, input, swipe, scroll等")
    target: str = Field(..., description="目标元素描述")
    description: str = Field(..., description="步骤描述")
    expected_result: Optional[str] = Field(None, description="预期结果")
    selector: Optional[str] = Field(None, description="元素选择器")
    input_value: Optional[str] = Field(None, description="输入值")
    coordinates: Optional[Dict[str, int]] = Field(None, description="坐标位置")
    swipe_direction: Optional[str] = Field(None, description="滑动方向")


class AndroidAnalysisResult(BaseModel):
    """Android界面分析结果"""
    ui_elements: List[AndroidUIElement] = Field(default_factory=list, description="识别的UI元素")
    test_scenarios: List[str] = Field(default_factory=list, description="测试场景")
    test_steps: List[AndroidTestStep] = Field(default_factory=list, description="测试步骤")
    analysis_summary: str = Field(..., description="分析总结")
    confidence_score: float = Field(default=0.0, description="置信度分数")
    activity_name: Optional[str] = Field(None, description="当前Activity名称")
    package_name: Optional[str] = Field(None, description="应用包名")
    screen_resolution: Optional[Dict[str, int]] = Field(None, description="屏幕分辨率")


class AndroidGeneratedScript(BaseModel):
    """Android生成的脚本"""
    format: str = Field(..., description="脚本格式: appium, uiautomator2, yaml")
    content: str = Field(..., description="脚本内容")
    file_path: Optional[str] = Field(None, description="文件路径")
    estimated_duration: Optional[str] = Field(None, description="预估执行时间")
    script_type: str = Field(default="automation", description="脚本类型")
    framework: str = Field(default="appium", description="测试框架")


# ============ Android智能体消息类型 ============

class AndroidAnalysisRequest(BaseMessage):
    """Android界面分析请求消息"""
    session_id: str = Field(..., description="会话ID")
    screenshot_data: str = Field(..., description="Base64编码的截图数据")
    ui_hierarchy: Optional[str] = Field(None, description="UI层次结构XML")
    test_description: str = Field(..., description="测试需求描述")
    additional_context: Optional[str] = Field(None, description="额外上下文信息")
    generate_formats: List[str] = Field(default=["appium"], description="生成格式列表")
    device_info: Optional[Dict[str, Any]] = Field(None, description="设备信息")
    app_package: Optional[str] = Field(None, description="应用包名")
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "uuid-string",
                "screenshot_data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
                "test_description": "测试Android应用登录功能",
                "additional_context": "需要验证输入验证和错误提示",
                "generate_formats": ["appium", "uiautomator2"],
                "device_info": {
                    "platform_name": "Android",
                    "platform_version": "11",
                    "device_name": "Pixel_4"
                },
                "app_package": "com.example.app"
            }
        }


class AndroidAnalysisResponse(BaseMessage):
    """Android界面分析响应消息"""
    session_id: str = Field(..., description="会话ID")
    analysis_result: AndroidAnalysisResult = Field(..., description="分析结果")
    generated_scripts: List[AndroidGeneratedScript] = Field(default_factory=list, description="生成的脚本")
    status: str = Field(..., description="处理状态")
    message: str = Field(..., description="响应消息")
    processing_time: float = Field(default=0.0, description="处理时间（秒）")


class AndroidUIAnalysisMessage(BaseMessage):
    """Android UI分析消息"""
    session_id: str = Field(..., description="会话ID")
    requirement_id: str = Field(..., description="需求ID")
    screenshot_data: str = Field(..., description="截图数据")
    ui_hierarchy: Optional[str] = Field(None, description="UI层次结构")
    test_description: str = Field(..., description="测试描述")
    analysis_context: Dict[str, Any] = Field(default_factory=dict, description="分析上下文")


class AndroidTestGenerationMessage(BaseMessage):
    """Android测试生成消息"""
    session_id: str = Field(..., description="会话ID")
    requirement_id: str = Field(..., description="需求ID")
    analysis_result: AndroidAnalysisResult = Field(..., description="分析结果")
    test_description: str = Field(..., description="测试描述")
    target_app: Optional[str] = Field(None, description="目标应用包名")
    generation_config: Dict[str, Any] = Field(default_factory=dict, description="生成配置")


class AndroidScriptGenerationMessage(BaseMessage):
    """Android脚本生成消息"""
    session_id: str = Field(..., description="会话ID")
    requirement_id: str = Field(..., description="需求ID")
    analysis_result: Dict[str, Any] = Field(..., description="分析结果")
    test_description: str = Field(..., description="测试描述")
    generate_formats: List[str] = Field(default_factory=lambda: ["midscene"], description="生成格式列表")
    additional_context: Optional[str] = Field(None, description="额外上下文信息")
    target_app: Optional[str] = Field(None, description="目标应用包名")
    generation_config: Dict[str, Any] = Field(default_factory=dict, description="生成配置")


class AndroidScriptExecutionMessage(BaseMessage):
    """Android脚本执行消息"""
    session_id: str = Field(..., description="会话ID")
    execution_id: str = Field(..., description="执行ID")
    script_type: str = Field(..., description="脚本类型: appium, uiautomator2")
    script_content: str = Field(..., description="脚本内容")
    execution_config: Dict[str, Any] = Field(default_factory=dict, description="执行配置")
    device_config: Dict[str, Any] = Field(default_factory=dict, description="设备配置")


class AndroidDeviceConnectionMessage(BaseMessage):
    """Android设备连接消息"""
    session_id: str = Field(..., description="会话ID")
    device_id: str = Field(..., description="设备ID")
    connection_type: str = Field(default="adb", description="连接类型: adb, wifi")
    device_capabilities: Dict[str, Any] = Field(default_factory=dict, description="设备能力")
    device_config: Dict[str, Any] = Field(default_factory=dict, description="设备配置")
    app_package: Optional[str] = Field(None, description="目标应用包名")
    app_activity: Optional[str] = Field(None, description="目标Activity")


class AndroidAppInstallMessage(BaseMessage):
    """Android应用安装消息"""
    session_id: str = Field(..., description="会话ID")
    device_id: str = Field(..., description="设备ID")
    apk_path: str = Field(..., description="APK文件路径")
    package_name: str = Field(..., description="应用包名")
    install_options: Dict[str, Any] = Field(default_factory=dict, description="安装选项")


class AndroidPerformanceTestMessage(BaseMessage):
    """Android性能测试消息"""
    session_id: str = Field(..., description="会话ID")
    device_id: str = Field(..., description="设备ID")
    package_name: str = Field(..., description="应用包名")
    test_duration: int = Field(default=60, description="测试持续时间（秒）")
    metrics: List[str] = Field(default=["cpu", "memory", "battery"], description="监控指标")
    test_scenarios: List[str] = Field(default_factory=list, description="测试场景")


# ============ Android设备管理相关消息 ============

class AndroidDeviceInfo(BaseModel):
    """Android设备信息"""
    device_id: str = Field(..., description="设备ID")
    device_name: Optional[str] = Field(None, description="设备名称")
    device_model: Optional[str] = Field(None, description="设备型号")
    manufacturer: Optional[str] = Field(None, description="制造商")

    # 系统信息
    android_version: Optional[str] = Field(None, description="Android版本")
    api_level: Optional[int] = Field(None, description="API级别")
    build_version: Optional[str] = Field(None, description="构建版本")

    # 硬件信息
    screen_resolution: Optional[str] = Field(None, description="屏幕分辨率")
    screen_density: Optional[str] = Field(None, description="屏幕密度")
    cpu_architecture: Optional[str] = Field(None, description="CPU架构")
    memory_total: Optional[str] = Field(None, description="总内存")

    # 连接信息
    connection_type: str = Field("usb", description="连接类型")
    ip_address: Optional[str] = Field(None, description="IP地址")
    adb_port: int = Field(5555, description="ADB端口")

    # 状态信息
    status: str = Field("offline", description="设备状态")
    last_seen: Optional[str] = Field(None, description="最后在线时间")


class AndroidGeneratedScript(BaseModel):
    """Android生成的脚本"""
    script_type: AndroidScriptType = Field(..., description="脚本类型")
    script_language: str = Field("python", description="脚本语言")
    script_content: str = Field(..., description="脚本内容")
    script_config: Optional[Dict[str, Any]] = Field(None, description="脚本配置")

    # 脚本元数据
    test_description: str = Field(..., description="测试描述")
    dependencies: List[str] = Field(default_factory=list, description="依赖包列表")
    setup_instructions: Optional[str] = Field(None, description="设置说明")
    execution_notes: Optional[str] = Field(None, description="执行注意事项")


# ============ Android API请求和响应模型 ============

class AndroidAnalysisRequest(BaseModel):
    """Android界面分析请求"""
    session_id: str = Field(..., description="会话ID")
    screenshot_data: str = Field(..., description="截图数据（base64编码）")
    ui_hierarchy: Optional[str] = Field(None, description="UI层次结构XML")

    # 设备信息
    device_id: Optional[str] = Field(None, description="设备ID")
    device_info: Optional[Dict[str, Any]] = Field(None, description="设备信息")

    # 应用信息
    package_name: Optional[str] = Field(None, description="应用包名")
    activity_name: Optional[str] = Field(None, description="当前Activity")

    # 分析配置
    test_description: str = Field("Android界面分析", description="测试描述")
    additional_context: Optional[str] = Field(None, description="额外上下文信息")
    generate_formats: List[str] = Field(default_factory=lambda: ["appium"], description="生成脚本格式")

    # 分析选项
    include_non_interactive: bool = Field(False, description="是否包含非交互元素")
    focus_areas: Optional[List[str]] = Field(None, description="重点分析区域")


class AndroidAnalysisResponse(BaseModel):
    """Android界面分析响应"""
    success: bool = Field(..., description="是否成功")
    session_id: str = Field(..., description="会话ID")
    analysis_id: str = Field(..., description="分析ID")

    # 分析结果
    analysis_result: AndroidAnalysisResult = Field(..., description="分析结果")

    # 生成的脚本
    generated_scripts: List[AndroidGeneratedScript] = Field(default_factory=list, description="生成的脚本")

    # 响应元数据
    message: str = Field("分析完成", description="响应消息")
    processing_time: float = Field(..., description="处理时间")
    timestamp: str = Field(..., description="时间戳")


class AndroidScriptGenerationRequest(BaseModel):
    """Android脚本生成请求"""
    session_id: str = Field(..., description="会话ID")
    analysis_id: str = Field(..., description="分析ID")

    # 脚本配置
    script_type: AndroidScriptType = Field(AndroidScriptType.APPIUM, description="脚本类型")
    script_language: str = Field("python", description="脚本语言")
    test_description: str = Field(..., description="测试描述")

    # 生成选项
    include_setup: bool = Field(True, description="是否包含设置代码")
    include_teardown: bool = Field(True, description="是否包含清理代码")
    use_page_object: bool = Field(True, description="是否使用Page Object模式")
    add_assertions: bool = Field(True, description="是否添加断言")


class AndroidExecutionRequest(BaseModel):
    """Android脚本执行请求"""
    session_id: str = Field(..., description="会话ID")
    script_id: str = Field(..., description="脚本ID")
    device_id: str = Field(..., description="目标设备ID")

    # 执行配置
    execution_config: Dict[str, Any] = Field(default_factory=dict, description="执行配置")

    # 执行选项
    capture_screenshots: bool = Field(True, description="是否捕获截图")
    record_video: bool = Field(False, description="是否录制视频")
    timeout_seconds: int = Field(300, description="超时时间（秒）")
