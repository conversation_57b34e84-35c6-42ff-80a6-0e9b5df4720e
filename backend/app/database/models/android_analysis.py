"""
Android分析结果数据模型
定义Android界面分析、设备管理、脚本执行等相关的数据库表结构
"""
from sqlalchemy import Column, String, Text, Integer, DECIMAL, Boolean, JSON, ForeignKey, Index, Enum
from sqlalchemy.orm import relationship
from typing import Dict, Any, Optional
from datetime import datetime

from .base import BaseModel


class AndroidAnalysisResult(BaseModel):
    """Android界面分析结果表模型"""
    
    __tablename__ = 'android_analysis_results'
    
    # 关联信息
    session_id = Column(String(100), nullable=False, index=True)
    analysis_id = Column(String(36), nullable=False, unique=True)
    
    # 应用基本信息
    app_name = Column(String(255), nullable=False)
    package_name = Column(String(255))
    activity_name = Column(String(255))
    app_version = Column(String(50))
    
    # 设备信息
    device_id = Column(String(100))
    device_model = Column(String(100))
    android_version = Column(String(50))
    screen_resolution = Column(String(50))
    
    # 分析结果
    analysis_summary = Column(Text)
    confidence_score = Column(DECIMAL(5, 2), default=0.0)
    
    # 完整的分析数据（JSON格式存储）
    raw_analysis_json = Column(JSON)  # 智能体输出的原始JSON数据
    parsed_ui_elements = Column(JSON)  # 解析后的UI元素数据
    ui_hierarchy_xml = Column(Text)  # UI层次结构XML
    analysis_metadata = Column(JSON)  # 分析元数据（处理时间、智能体信息等）
    
    # 统计信息
    elements_count = Column(Integer, default=0)
    processing_time = Column(DECIMAL(10, 3))  # 处理时间（秒）
    
    # 关系
    android_elements = relationship("AndroidElement", back_populates="android_analysis", cascade="all, delete-orphan")
    android_scripts = relationship("AndroidScript", back_populates="android_analysis")
    
    # 索引
    __table_args__ = (
        Index('idx_android_analysis_session_id', 'session_id'),
        Index('idx_android_analysis_app_name', 'app_name'),
        Index('idx_android_analysis_package_name', 'package_name'),
        Index('idx_android_analysis_device_id', 'device_id'),
        Index('idx_android_analysis_created_at', 'created_at'),
        Index('idx_android_analysis_confidence', 'confidence_score'),
    )
    
    def __repr__(self):
        return f"<AndroidAnalysisResult(id={self.id}, app_name={self.app_name}, confidence={self.confidence_score})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，用于API响应"""
        def safe_serialize(value):
            """安全序列化值"""
            if value is None:
                return None
            if isinstance(value, (str, int, float, bool, list, dict)):
                return value
            if hasattr(value, 'isoformat'):  # datetime对象
                return value.isoformat()
            return str(value)

        return {
            "id": safe_serialize(self.id),
            "session_id": safe_serialize(self.session_id),
            "analysis_id": safe_serialize(self.analysis_id),
            "app_name": safe_serialize(self.app_name),
            "package_name": safe_serialize(self.package_name),
            "activity_name": safe_serialize(self.activity_name),
            "app_version": safe_serialize(self.app_version),
            "device_id": safe_serialize(self.device_id),
            "device_model": safe_serialize(self.device_model),
            "android_version": safe_serialize(self.android_version),
            "screen_resolution": safe_serialize(self.screen_resolution),
            "analysis_summary": safe_serialize(self.analysis_summary),
            "confidence_score": safe_serialize(self.confidence_score),
            "raw_analysis_json": safe_serialize(self.raw_analysis_json),
            "parsed_ui_elements": safe_serialize(self.parsed_ui_elements),
            "ui_hierarchy_xml": safe_serialize(self.ui_hierarchy_xml),
            "analysis_metadata": safe_serialize(self.analysis_metadata),
            "elements_count": safe_serialize(self.elements_count),
            "processing_time": safe_serialize(self.processing_time),
            "created_at": safe_serialize(self.created_at),
            "updated_at": safe_serialize(self.updated_at)
        }


class AndroidElement(BaseModel):
    """Android UI元素表模型"""
    
    __tablename__ = 'android_elements'
    
    # 关联Android分析结果
    android_analysis_id = Column(String(36), ForeignKey('android_analysis_results.id', ondelete='CASCADE'), nullable=False)
    
    # 元素基本信息
    element_name = Column(String(255))
    element_type = Column(String(100), nullable=False)  # Button, EditText, TextView, etc.
    element_description = Column(Text)
    
    # Android特有属性
    resource_id = Column(String(255))  # Android resource-id
    class_name = Column(String(255))   # Android class name
    text = Column(Text)                # 元素文本内容
    content_desc = Column(Text)        # content-description
    
    # 位置和尺寸信息
    bounds = Column(String(100))       # 元素边界坐标
    clickable = Column(Boolean, default=False)
    enabled = Column(Boolean, default=True)
    focusable = Column(Boolean, default=False)
    scrollable = Column(Boolean, default=False)
    
    # 元素完整信息（JSON格式存储）
    element_data = Column(JSON)  # 完整的元素数据（所有属性、层次关系等）
    
    # 核心属性（用于快速查询和索引）
    confidence_score = Column(DECIMAL(5, 2), default=0.0)
    is_testable = Column(Boolean, default=True)  # 是否可测试
    
    # 关系
    android_analysis = relationship("AndroidAnalysisResult", back_populates="android_elements")
    
    # 索引
    __table_args__ = (
        Index('idx_android_element_analysis_id', 'android_analysis_id'),
        Index('idx_android_element_type', 'element_type'),
        Index('idx_android_element_resource_id', 'resource_id'),
        Index('idx_android_element_class_name', 'class_name'),
        Index('idx_android_element_clickable', 'clickable'),
        Index('idx_android_element_testable', 'is_testable'),
    )
    
    def __repr__(self):
        return f"<AndroidElement(id={self.id}, type={self.element_type}, resource_id={self.resource_id})>"


class AndroidDevice(BaseModel):
    """Android设备信息表模型"""
    
    __tablename__ = 'android_devices'
    
    # 设备基本信息
    device_id = Column(String(100), nullable=False, unique=True)  # ADB设备ID
    device_name = Column(String(255))
    device_model = Column(String(100))
    manufacturer = Column(String(100))
    
    # 系统信息
    android_version = Column(String(50))
    api_level = Column(Integer)
    build_version = Column(String(100))
    
    # 硬件信息
    screen_resolution = Column(String(50))
    screen_density = Column(String(20))
    cpu_architecture = Column(String(50))
    memory_total = Column(String(20))
    
    # 连接信息
    connection_type = Column(Enum('usb', 'wifi', 'emulator'), default='usb')
    ip_address = Column(String(50))
    adb_port = Column(Integer, default=5555)
    
    # 状态信息
    status = Column(Enum('online', 'offline', 'unauthorized', 'error'), default='offline')
    last_seen = Column(String(50))  # 最后在线时间
    
    # 设备配置（JSON格式存储）
    device_config = Column(JSON)  # 设备配置信息
    capabilities = Column(JSON)   # 设备能力信息
    
    # 关系
    android_executions = relationship("AndroidExecution", back_populates="device")
    
    # 索引
    __table_args__ = (
        Index('idx_android_device_id', 'device_id'),
        Index('idx_android_device_status', 'status'),
        Index('idx_android_device_model', 'device_model'),
        Index('idx_android_device_connection', 'connection_type'),
    )
    
    def __repr__(self):
        return f"<AndroidDevice(id={self.device_id}, model={self.device_model}, status={self.status})>"


class AndroidScript(BaseModel):
    """Android自动化脚本表模型"""
    
    __tablename__ = 'android_scripts'
    
    # 关联分析结果
    android_analysis_id = Column(String(36), ForeignKey('android_analysis_results.id', ondelete='SET NULL'))
    session_id = Column(String(100), nullable=False, index=True)
    
    # 脚本基本信息
    script_name = Column(String(255), nullable=False)
    script_type = Column(Enum('appium', 'uiautomator2', 'midscene'), default='appium')
    script_language = Column(Enum('python', 'java', 'javascript', 'typescript'), default='python')
    
    # 脚本内容
    script_content = Column(Text, nullable=False)
    script_config = Column(JSON)  # 脚本配置信息
    
    # 测试信息
    test_description = Column(Text)
    test_scenarios = Column(JSON)  # 测试场景列表
    
    # 生成信息
    generated_by = Column(String(100), default='AndroidScriptGeneratorAgent')
    generation_metadata = Column(JSON)  # 生成元数据
    
    # 关系
    android_analysis = relationship("AndroidAnalysisResult", back_populates="android_scripts")
    android_executions = relationship("AndroidExecution", back_populates="script")
    
    # 索引
    __table_args__ = (
        Index('idx_android_script_session_id', 'session_id'),
        Index('idx_android_script_type', 'script_type'),
        Index('idx_android_script_language', 'script_language'),
        Index('idx_android_script_analysis_id', 'android_analysis_id'),
    )
    
    def __repr__(self):
        return f"<AndroidScript(id={self.id}, name={self.script_name}, type={self.script_type})>"


class AndroidExecution(BaseModel):
    """Android脚本执行记录表模型"""
    
    __tablename__ = 'android_executions'
    
    # 关联信息
    script_id = Column(String(36), ForeignKey('android_scripts.id', ondelete='CASCADE'), nullable=False)
    device_id = Column(String(100), ForeignKey('android_devices.device_id', ondelete='SET NULL'))
    session_id = Column(String(100), nullable=False, index=True)
    
    # 执行信息
    execution_name = Column(String(255))
    execution_status = Column(Enum('pending', 'running', 'completed', 'failed', 'cancelled'), default='pending')
    
    # 执行配置
    execution_config = Column(JSON)  # 执行配置参数
    
    # 执行结果
    start_time = Column(String(50))
    end_time = Column(String(50))
    duration_seconds = Column(Integer)
    
    # 结果数据
    execution_result = Column(JSON)  # 执行结果详情
    test_results = Column(JSON)      # 测试结果统计
    error_message = Column(Text)     # 错误信息
    
    # 执行日志和截图
    execution_logs = Column(Text)    # 执行日志
    screenshots = Column(JSON)      # 截图列表
    video_path = Column(String(500)) # 录屏文件路径
    
    # 关系
    script = relationship("AndroidScript", back_populates="android_executions")
    device = relationship("AndroidDevice", back_populates="android_executions")
    
    # 索引
    __table_args__ = (
        Index('idx_android_execution_script_id', 'script_id'),
        Index('idx_android_execution_device_id', 'device_id'),
        Index('idx_android_execution_session_id', 'session_id'),
        Index('idx_android_execution_status', 'execution_status'),
        Index('idx_android_execution_start_time', 'start_time'),
    )
    
    def __repr__(self):
        return f"<AndroidExecution(id={self.id}, status={self.execution_status}, device={self.device_id})>"
