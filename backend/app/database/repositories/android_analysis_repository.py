"""
Android分析数据仓库
提供Android界面分析结果的数据访问层
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from loguru import logger

from .base import BaseRepository
from ..models.android_analysis import AndroidAnalysisResult, AndroidElement, AndroidDevice, AndroidScript, AndroidExecution


class AndroidAnalysisRepository(BaseRepository[AndroidAnalysisResult]):
    """Android分析结果仓库"""
    
    def __init__(self):
        super().__init__(AndroidAnalysisResult)
    
    async def create_with_elements(self, 
                                 session: AsyncSession,
                                 analysis_data: Dict[str, Any],
                                 elements_data: List[Dict[str, Any]]) -> AndroidAnalysisResult:
        """创建Android分析结果及其元素"""
        try:
            # 创建Android分析结果
            android_analysis = await self.create(session, **analysis_data)
            
            # 创建Android元素
            for element_data in elements_data:
                element_data['android_analysis_id'] = android_analysis.id
                android_element = AndroidElement(**element_data)
                session.add(android_element)
            
            # 更新元素数量
            android_analysis.elements_count = len(elements_data)
            
            await session.flush()
            await session.refresh(android_analysis)
            
            return android_analysis
            
        except Exception as e:
            logger.error(f"创建Android分析结果及元素失败: {e}")
            raise
    
    async def get_by_analysis_id(self, session: AsyncSession, analysis_id: str) -> Optional[AndroidAnalysisResult]:
        """根据分析ID获取Android分析结果"""
        try:
            result = await session.execute(
                select(AndroidAnalysisResult).where(AndroidAnalysisResult.analysis_id == analysis_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"根据分析ID获取Android分析结果失败: {e}")
            raise
    
    async def get_with_elements(self, session: AsyncSession, analysis_id: str) -> Optional[Dict[str, Any]]:
        """获取Android分析结果及其元素"""
        try:
            # 获取分析结果
            analysis_result = await session.execute(
                select(AndroidAnalysisResult).where(AndroidAnalysisResult.analysis_id == analysis_id)
            )
            analysis = analysis_result.scalar_one_or_none()
            
            if not analysis:
                return None
            
            # 获取相关元素
            elements_result = await session.execute(
                select(AndroidElement).where(AndroidElement.android_analysis_id == analysis.id)
            )
            elements = elements_result.scalars().all()
            
            return {
                "analysis": analysis.to_dict(),
                "elements": [
                    {
                        "id": str(element.id),
                        "element_name": element.element_name,
                        "element_type": element.element_type,
                        "element_description": element.element_description,
                        "resource_id": element.resource_id,
                        "class_name": element.class_name,
                        "text": element.text,
                        "content_desc": element.content_desc,
                        "bounds": element.bounds,
                        "clickable": element.clickable,
                        "enabled": element.enabled,
                        "focusable": element.focusable,
                        "scrollable": element.scrollable,
                        "element_data": element.element_data,
                        "confidence_score": float(element.confidence_score) if element.confidence_score else 0.0,
                        "is_testable": element.is_testable,
                        "created_at": element.created_at.isoformat() if element.created_at else None
                    }
                    for element in elements
                ]
            }
            
        except Exception as e:
            logger.error(f"获取Android分析结果及元素失败: {e}")
            raise
    
    async def search_by_app_name(self, session: AsyncSession, app_name: str, limit: int = 20) -> List[AndroidAnalysisResult]:
        """根据应用名称搜索Android分析结果"""
        try:
            result = await session.execute(
                select(AndroidAnalysisResult)
                .where(AndroidAnalysisResult.app_name.ilike(f"%{app_name}%"))
                .order_by(desc(AndroidAnalysisResult.created_at))
                .limit(limit)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"根据应用名称搜索Android分析结果失败: {e}")
            raise
    
    async def get_by_session_id(self, session: AsyncSession, session_id: str) -> List[AndroidAnalysisResult]:
        """根据会话ID获取Android分析结果"""
        try:
            result = await session.execute(
                select(AndroidAnalysisResult)
                .where(AndroidAnalysisResult.session_id == session_id)
                .order_by(desc(AndroidAnalysisResult.created_at))
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"根据会话ID获取Android分析结果失败: {e}")
            raise
    
    async def get_by_package_name(self, session: AsyncSession, package_name: str) -> List[AndroidAnalysisResult]:
        """根据包名获取Android分析结果"""
        try:
            result = await session.execute(
                select(AndroidAnalysisResult)
                .where(AndroidAnalysisResult.package_name == package_name)
                .order_by(desc(AndroidAnalysisResult.created_at))
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"根据包名获取Android分析结果失败: {e}")
            raise
    
    async def get_statistics(self, session: AsyncSession) -> Dict[str, Any]:
        """获取Android分析统计信息"""
        try:
            # 总分析数量
            total_count = await session.execute(
                select(func.count(AndroidAnalysisResult.id))
            )
            total = total_count.scalar()
            
            # 按设备统计
            device_stats = await session.execute(
                select(
                    AndroidAnalysisResult.device_model,
                    func.count(AndroidAnalysisResult.id).label('count')
                )
                .group_by(AndroidAnalysisResult.device_model)
                .order_by(desc('count'))
            )
            
            # 按应用统计
            app_stats = await session.execute(
                select(
                    AndroidAnalysisResult.package_name,
                    func.count(AndroidAnalysisResult.id).label('count')
                )
                .group_by(AndroidAnalysisResult.package_name)
                .order_by(desc('count'))
                .limit(10)
            )
            
            # 平均置信度
            avg_confidence = await session.execute(
                select(func.avg(AndroidAnalysisResult.confidence_score))
            )
            
            return {
                "total_analyses": total,
                "device_distribution": [
                    {"device_model": row[0], "count": row[1]}
                    for row in device_stats.fetchall()
                ],
                "top_apps": [
                    {"package_name": row[0], "count": row[1]}
                    for row in app_stats.fetchall()
                ],
                "average_confidence": float(avg_confidence.scalar() or 0.0)
            }
            
        except Exception as e:
            logger.error(f"获取Android分析统计信息失败: {e}")
            raise


class AndroidDeviceRepository(BaseRepository[AndroidDevice]):
    """Android设备仓库"""
    
    def __init__(self):
        super().__init__(AndroidDevice)
    
    async def get_by_device_id(self, session: AsyncSession, device_id: str) -> Optional[AndroidDevice]:
        """根据设备ID获取设备信息"""
        try:
            result = await session.execute(
                select(AndroidDevice).where(AndroidDevice.device_id == device_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"根据设备ID获取设备信息失败: {e}")
            raise
    
    async def get_online_devices(self, session: AsyncSession) -> List[AndroidDevice]:
        """获取在线设备列表"""
        try:
            result = await session.execute(
                select(AndroidDevice)
                .where(AndroidDevice.status == 'online')
                .order_by(AndroidDevice.device_name)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"获取在线设备列表失败: {e}")
            raise
    
    async def update_device_status(self, session: AsyncSession, device_id: str, status: str, last_seen: str = None) -> bool:
        """更新设备状态"""
        try:
            device = await self.get_by_device_id(session, device_id)
            if device:
                device.status = status
                if last_seen:
                    device.last_seen = last_seen
                await session.commit()
                return True
            return False
        except Exception as e:
            logger.error(f"更新设备状态失败: {e}")
            raise


class AndroidScriptRepository(BaseRepository[AndroidScript]):
    """Android脚本仓库"""
    
    def __init__(self):
        super().__init__(AndroidScript)
    
    async def get_by_session_id(self, session: AsyncSession, session_id: str) -> List[AndroidScript]:
        """根据会话ID获取脚本列表"""
        try:
            result = await session.execute(
                select(AndroidScript)
                .where(AndroidScript.session_id == session_id)
                .order_by(desc(AndroidScript.created_at))
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"根据会话ID获取脚本列表失败: {e}")
            raise
    
    async def get_by_analysis_id(self, session: AsyncSession, analysis_id: str) -> List[AndroidScript]:
        """根据分析ID获取脚本列表"""
        try:
            result = await session.execute(
                select(AndroidScript)
                .where(AndroidScript.android_analysis_id == analysis_id)
                .order_by(desc(AndroidScript.created_at))
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"根据分析ID获取脚本列表失败: {e}")
            raise


class AndroidExecutionRepository(BaseRepository[AndroidExecution]):
    """Android执行记录仓库"""
    
    def __init__(self):
        super().__init__(AndroidExecution)
    
    async def get_by_session_id(self, session: AsyncSession, session_id: str) -> List[AndroidExecution]:
        """根据会话ID获取执行记录"""
        try:
            result = await session.execute(
                select(AndroidExecution)
                .where(AndroidExecution.session_id == session_id)
                .order_by(desc(AndroidExecution.created_at))
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"根据会话ID获取执行记录失败: {e}")
            raise
    
    async def get_running_executions(self, session: AsyncSession) -> List[AndroidExecution]:
        """获取正在运行的执行记录"""
        try:
            result = await session.execute(
                select(AndroidExecution)
                .where(AndroidExecution.execution_status.in_(['pending', 'running']))
                .order_by(desc(AndroidExecution.created_at))
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"获取正在运行的执行记录失败: {e}")
            raise
