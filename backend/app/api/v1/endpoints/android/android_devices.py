"""
Android设备管理API端点
提供Android设备连接、监控和管理功能
"""
import asyncio
import subprocess
import json
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import J<PERSON><PERSON>esponse
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.database.connection import get_database
from app.database.repositories.android_analysis_repository import AndroidDeviceRepository
from app.core.messages.android import AndroidDeviceInfo
from app.agents.android import AndroidDeviceManagerAgent

router = APIRouter()

# 设备状态缓存
device_cache: Dict[str, AndroidDeviceInfo] = {}


@router.get("/scan")
async def scan_android_devices(
    background_tasks: BackgroundTasks,
    session: AsyncSession = Depends(get_database)
):
    """
    扫描连接的Android设备
    
    Returns:
        设备列表
    """
    try:
        logger.info("开始扫描Android设备")
        
        # 使用设备管理智能体扫描设备
        device_manager = AndroidDeviceManagerAgent()
        devices = await device_manager.scan_devices()
        
        # 更新数据库中的设备信息
        device_repo = AndroidDeviceRepository()
        
        for device_info in devices:
            try:
                # 检查设备是否已存在
                existing_device = await device_repo.get_by_device_id(session, device_info["device_id"])
                
                if existing_device:
                    # 更新现有设备
                    existing_device.status = device_info.get("status", "online")
                    existing_device.last_seen = device_info.get("last_seen")
                    existing_device.device_name = device_info.get("device_name", existing_device.device_name)
                    existing_device.device_model = device_info.get("device_model", existing_device.device_model)
                    existing_device.android_version = device_info.get("android_version", existing_device.android_version)
                    existing_device.screen_resolution = device_info.get("screen_resolution", existing_device.screen_resolution)
                else:
                    # 创建新设备
                    await device_repo.create(session, **device_info)
                
            except Exception as e:
                logger.error(f"更新设备信息失败: {device_info.get('device_id')}, 错误: {e}")
        
        await session.commit()
        
        # 更新缓存
        for device_info in devices:
            device_cache[device_info["device_id"]] = AndroidDeviceInfo(**device_info)
        
        return JSONResponse({
            "success": True,
            "data": devices,
            "message": f"扫描完成，发现 {len(devices)} 个设备"
        })
        
    except Exception as e:
        logger.error(f"扫描Android设备失败: {e}")
        raise HTTPException(status_code=500, detail=f"扫描设备失败: {str(e)}")


@router.get("/list")
async def list_android_devices(session: AsyncSession = Depends(get_database)):
    """
    获取设备列表
    
    Returns:
        设备列表
    """
    try:
        device_repo = AndroidDeviceRepository()
        devices = await device_repo.get_all(session)
        
        return JSONResponse({
            "success": True,
            "data": [
                {
                    "id": str(device.id),
                    "device_id": device.device_id,
                    "device_name": device.device_name,
                    "device_model": device.device_model,
                    "manufacturer": device.manufacturer,
                    "android_version": device.android_version,
                    "api_level": device.api_level,
                    "screen_resolution": device.screen_resolution,
                    "connection_type": device.connection_type,
                    "status": device.status,
                    "last_seen": device.last_seen,
                    "created_at": device.created_at.isoformat() if device.created_at else None
                }
                for device in devices
            ]
        })
        
    except Exception as e:
        logger.error(f"获取设备列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取设备列表失败: {str(e)}")


@router.get("/online")
async def get_online_devices(session: AsyncSession = Depends(get_database)):
    """
    获取在线设备列表
    
    Returns:
        在线设备列表
    """
    try:
        device_repo = AndroidDeviceRepository()
        devices = await device_repo.get_online_devices(session)
        
        return JSONResponse({
            "success": True,
            "data": [
                {
                    "device_id": device.device_id,
                    "device_name": device.device_name,
                    "device_model": device.device_model,
                    "android_version": device.android_version,
                    "screen_resolution": device.screen_resolution,
                    "status": device.status
                }
                for device in devices
            ]
        })
        
    except Exception as e:
        logger.error(f"获取在线设备失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取在线设备失败: {str(e)}")


@router.get("/{device_id}/info")
async def get_device_info(
    device_id: str,
    session: AsyncSession = Depends(get_database)
):
    """
    获取设备详细信息
    
    Args:
        device_id: 设备ID
    
    Returns:
        设备详细信息
    """
    try:
        device_repo = AndroidDeviceRepository()
        device = await device_repo.get_by_device_id(session, device_id)
        
        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")
        
        # 获取实时设备信息
        device_manager = AndroidDeviceManagerAgent()
        real_time_info = await device_manager.get_device_info(device_id)
        
        return JSONResponse({
            "success": True,
            "data": {
                "id": str(device.id),
                "device_id": device.device_id,
                "device_name": device.device_name,
                "device_model": device.device_model,
                "manufacturer": device.manufacturer,
                "android_version": device.android_version,
                "api_level": device.api_level,
                "build_version": device.build_version,
                "screen_resolution": device.screen_resolution,
                "screen_density": device.screen_density,
                "cpu_architecture": device.cpu_architecture,
                "memory_total": device.memory_total,
                "connection_type": device.connection_type,
                "ip_address": device.ip_address,
                "adb_port": device.adb_port,
                "status": device.status,
                "last_seen": device.last_seen,
                "device_config": device.device_config,
                "capabilities": device.capabilities,
                "real_time_info": real_time_info,
                "created_at": device.created_at.isoformat() if device.created_at else None,
                "updated_at": device.updated_at.isoformat() if device.updated_at else None
            }
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备信息失败: {device_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取设备信息失败: {str(e)}")


@router.post("/{device_id}/screenshot")
async def capture_device_screenshot(device_id: str):
    """
    捕获设备截图
    
    Args:
        device_id: 设备ID
    
    Returns:
        截图数据
    """
    try:
        device_manager = AndroidDeviceManagerAgent()
        screenshot_data = await device_manager.capture_screenshot(device_id)
        
        return JSONResponse({
            "success": True,
            "data": {
                "device_id": device_id,
                "screenshot_data": screenshot_data,
                "timestamp": asyncio.get_event_loop().time()
            },
            "message": "截图捕获成功"
        })
        
    except Exception as e:
        logger.error(f"捕获设备截图失败: {device_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"截图失败: {str(e)}")


@router.post("/{device_id}/ui-hierarchy")
async def get_ui_hierarchy(device_id: str):
    """
    获取设备UI层次结构
    
    Args:
        device_id: 设备ID
    
    Returns:
        UI层次结构XML
    """
    try:
        device_manager = AndroidDeviceManagerAgent()
        ui_hierarchy = await device_manager.get_ui_hierarchy(device_id)
        
        return JSONResponse({
            "success": True,
            "data": {
                "device_id": device_id,
                "ui_hierarchy": ui_hierarchy,
                "timestamp": asyncio.get_event_loop().time()
            },
            "message": "UI层次结构获取成功"
        })
        
    except Exception as e:
        logger.error(f"获取UI层次结构失败: {device_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取UI层次结构失败: {str(e)}")


@router.post("/{device_id}/install-app")
async def install_app(
    device_id: str,
    apk_path: str,
    package_name: Optional[str] = None
):
    """
    在设备上安装应用
    
    Args:
        device_id: 设备ID
        apk_path: APK文件路径
        package_name: 应用包名
    
    Returns:
        安装结果
    """
    try:
        device_manager = AndroidDeviceManagerAgent()
        result = await device_manager.install_app(device_id, apk_path, package_name)
        
        return JSONResponse({
            "success": True,
            "data": result,
            "message": "应用安装成功"
        })
        
    except Exception as e:
        logger.error(f"安装应用失败: {device_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"安装应用失败: {str(e)}")


@router.post("/{device_id}/uninstall-app")
async def uninstall_app(device_id: str, package_name: str):
    """
    卸载设备上的应用
    
    Args:
        device_id: 设备ID
        package_name: 应用包名
    
    Returns:
        卸载结果
    """
    try:
        device_manager = AndroidDeviceManagerAgent()
        result = await device_manager.uninstall_app(device_id, package_name)
        
        return JSONResponse({
            "success": True,
            "data": result,
            "message": "应用卸载成功"
        })
        
    except Exception as e:
        logger.error(f"卸载应用失败: {device_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"卸载应用失败: {str(e)}")


@router.get("/{device_id}/apps")
async def list_installed_apps(device_id: str):
    """
    获取设备已安装应用列表
    
    Args:
        device_id: 设备ID
    
    Returns:
        已安装应用列表
    """
    try:
        device_manager = AndroidDeviceManagerAgent()
        apps = await device_manager.list_installed_apps(device_id)
        
        return JSONResponse({
            "success": True,
            "data": apps,
            "message": f"获取到 {len(apps)} 个已安装应用"
        })
        
    except Exception as e:
        logger.error(f"获取已安装应用失败: {device_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取应用列表失败: {str(e)}")


@router.post("/{device_id}/start-app")
async def start_app(device_id: str, package_name: str, activity_name: Optional[str] = None):
    """
    启动设备上的应用
    
    Args:
        device_id: 设备ID
        package_name: 应用包名
        activity_name: Activity名称
    
    Returns:
        启动结果
    """
    try:
        device_manager = AndroidDeviceManagerAgent()
        result = await device_manager.start_app(device_id, package_name, activity_name)
        
        return JSONResponse({
            "success": True,
            "data": result,
            "message": "应用启动成功"
        })
        
    except Exception as e:
        logger.error(f"启动应用失败: {device_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"启动应用失败: {str(e)}")


@router.post("/{device_id}/stop-app")
async def stop_app(device_id: str, package_name: str):
    """
    停止设备上的应用
    
    Args:
        device_id: 设备ID
        package_name: 应用包名
    
    Returns:
        停止结果
    """
    try:
        device_manager = AndroidDeviceManagerAgent()
        result = await device_manager.stop_app(device_id, package_name)
        
        return JSONResponse({
            "success": True,
            "data": result,
            "message": "应用停止成功"
        })
        
    except Exception as e:
        logger.error(f"停止应用失败: {device_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"停止应用失败: {str(e)}")
