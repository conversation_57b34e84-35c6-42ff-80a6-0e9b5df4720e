"""
Android脚本执行API端点
提供Android自动化脚本的执行、监控和结果管理功能
"""
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Request
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.database.connection import get_database
from app.database.repositories.android_analysis_repository import (
    AndroidScriptRepository, 
    AndroidExecutionRepository,
    AndroidDeviceRepository
)
from app.core.messages.android import AndroidExecutionRequest
from app.agents.android import AndroidExecutorAgent

router = APIRouter()

# 执行会话管理
execution_sessions: Dict[str, Dict[str, Any]] = {}
execution_queues: Dict[str, asyncio.Queue] = {}


@router.post("/start")
async def start_android_execution(
    request: AndroidExecutionRequest,
    background_tasks: BackgroundTasks,
    session: AsyncSession = Depends(get_database)
):
    """
    启动Android脚本执行
    
    Args:
        request: 执行请求
        background_tasks: 后台任务管理器
        session: 数据库会话
    
    Returns:
        执行会话信息
    """
    try:
        execution_id = str(uuid.uuid4())
        
        logger.info(f"开始Android脚本执行: {execution_id}")
        
        # 验证脚本存在
        script_repo = AndroidScriptRepository()
        script = await script_repo.get_by_id(session, request.script_id)
        
        if not script:
            raise HTTPException(status_code=404, detail="脚本不存在")
        
        # 验证设备存在
        device_repo = AndroidDeviceRepository()
        device = await device_repo.get_by_device_id(session, request.device_id)
        
        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")
        
        if device.status != 'online':
            raise HTTPException(status_code=400, detail="设备不在线")
        
        # 创建执行记录
        execution_repo = AndroidExecutionRepository()
        execution_data = {
            "script_id": script.id,
            "device_id": request.device_id,
            "session_id": request.session_id,
            "execution_name": f"执行_{script.script_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "execution_status": "pending",
            "execution_config": request.execution_config,
            "start_time": datetime.now().isoformat()
        }
        
        execution_record = await execution_repo.create(session, **execution_data)
        await session.commit()
        
        # 创建执行会话信息
        session_info = {
            "execution_id": execution_id,
            "script_id": request.script_id,
            "device_id": request.device_id,
            "session_id": request.session_id,
            "status": "pending",
            "created_at": datetime.now().isoformat(),
            "last_activity": datetime.now().isoformat(),
            "progress": 0,
            "execution_record_id": str(execution_record.id)
        }
        
        # 存储执行会话信息
        execution_sessions[execution_id] = session_info
        
        # 创建消息队列
        message_queue = asyncio.Queue()
        execution_queues[execution_id] = message_queue
        
        # 启动后台执行任务
        background_tasks.add_task(
            process_android_execution,
            execution_id,
            script,
            device,
            request,
            session
        )
        
        return JSONResponse({
            "success": True,
            "execution_id": execution_id,
            "script_id": request.script_id,
            "device_id": request.device_id,
            "message": "Android脚本执行已启动",
            "stream_url": f"/api/v1/android/execution/stream/{execution_id}"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动Android脚本执行失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动执行失败: {str(e)}")


async def process_android_execution(
    execution_id: str,
    script: Any,
    device: Any,
    request: AndroidExecutionRequest,
    db_session: AsyncSession
):
    """处理Android脚本执行的后台任务"""
    try:
        logger.info(f"开始处理Android脚本执行: {execution_id}")
        
        # 更新执行状态
        if execution_id in execution_sessions:
            execution_sessions[execution_id]["status"] = "running"
            execution_sessions[execution_id]["progress"] = 10
        
        # 发送初始消息
        await send_execution_message(execution_id, {
            "type": "progress",
            "message": "🚀 开始执行Android脚本...",
            "progress": 10
        })
        
        # 创建Android执行智能体
        executor_agent = AndroidExecutorAgent()
        
        # 准备执行配置
        execution_config = {
            **request.execution_config,
            "device_id": request.device_id,
            "capture_screenshots": request.capture_screenshots,
            "record_video": request.record_video,
            "timeout_seconds": request.timeout_seconds
        }
        
        # 执行脚本
        await send_execution_message(execution_id, {
            "type": "progress",
            "message": "▶️ 正在执行脚本...",
            "progress": 30
        })
        
        execution_result = await executor_agent.execute_script(
            script_content=script.script_content,
            script_type=script.script_type,
            device_config={
                "device_id": request.device_id,
                "device_model": device.device_model,
                "android_version": device.android_version,
                "screen_resolution": device.screen_resolution
            },
            execution_config=execution_config,
            session_id=request.session_id
        )
        
        # 更新执行记录
        execution_repo = AndroidExecutionRepository()
        execution_record_id = execution_sessions[execution_id]["execution_record_id"]
        
        await execution_repo.update(db_session, execution_record_id, **{
            "execution_status": "completed" if execution_result.get("success") else "failed",
            "end_time": datetime.now().isoformat(),
            "duration_seconds": execution_result.get("duration_seconds", 0),
            "execution_result": execution_result,
            "test_results": execution_result.get("test_results", {}),
            "execution_logs": execution_result.get("logs", ""),
            "screenshots": execution_result.get("screenshots", []),
            "video_path": execution_result.get("video_path"),
            "error_message": execution_result.get("error_message")
        })
        
        await db_session.commit()
        
        # 发送完成消息
        await send_execution_message(execution_id, {
            "type": "complete",
            "message": "✅ Android脚本执行完成！" if execution_result.get("success") else "❌ 脚本执行失败",
            "progress": 100,
            "result": execution_result
        })
        
        # 更新执行状态
        if execution_id in execution_sessions:
            execution_sessions[execution_id]["status"] = "completed" if execution_result.get("success") else "failed"
            execution_sessions[execution_id]["progress"] = 100
            execution_sessions[execution_id]["completed_at"] = datetime.now().isoformat()
            execution_sessions[execution_id]["result"] = execution_result
        
        logger.info(f"Android脚本执行完成: {execution_id}")
        
    except Exception as e:
        logger.error(f"Android脚本执行失败: {execution_id}, 错误: {e}")
        
        # 发送错误消息
        await send_execution_message(execution_id, {
            "type": "error",
            "message": f"❌ 执行失败: {str(e)}",
            "error": str(e)
        })
        
        # 更新执行状态
        if execution_id in execution_sessions:
            execution_sessions[execution_id]["status"] = "failed"
            execution_sessions[execution_id]["error"] = str(e)


async def send_execution_message(execution_id: str, message: Dict[str, Any]):
    """发送执行消息到队列"""
    if execution_id in execution_queues:
        try:
            await execution_queues[execution_id].put(message)
        except Exception as e:
            logger.error(f"发送执行消息到队列失败: {execution_id}, 错误: {e}")


@router.get("/stream/{execution_id}")
async def stream_android_execution(execution_id: str, request: Request):
    """
    Android执行SSE流式端点
    
    Args:
        execution_id: 执行ID
        request: HTTP请求对象
    
    Returns:
        EventSourceResponse: SSE响应流
    """
    # 验证执行会话是否存在
    if execution_id not in execution_sessions:
        raise HTTPException(status_code=404, detail=f"执行会话 {execution_id} 不存在或已过期")
    
    logger.info(f"开始Android执行SSE流: {execution_id}")
    
    # 确保消息队列存在
    if execution_id not in execution_queues:
        message_queue = asyncio.Queue()
        execution_queues[execution_id] = message_queue
    else:
        message_queue = execution_queues[execution_id]
    
    async def event_generator():
        try:
            while True:
                # 检查客户端是否断开连接
                if await request.is_disconnected():
                    logger.info(f"客户端断开连接: {execution_id}")
                    break
                
                try:
                    # 等待消息，设置超时避免无限等待
                    message = await asyncio.wait_for(message_queue.get(), timeout=1.0)
                    
                    # 发送消息
                    yield f"data: {message}\n\n"
                    
                    # 如果是完成或错误消息，结束流
                    if isinstance(message, dict) and message.get("type") in ["complete", "error"]:
                        break
                        
                except asyncio.TimeoutError:
                    # 发送心跳消息
                    yield f"data: {{'type': 'heartbeat', 'timestamp': '{datetime.now().isoformat()}'}}\n\n"
                    continue
                    
        except Exception as e:
            logger.error(f"执行SSE流错误: {execution_id}, 错误: {e}")
            yield f"data: {{'type': 'error', 'message': '流连接错误: {str(e)}'}}\n\n"
        
        finally:
            # 清理资源
            if execution_id in execution_queues:
                del execution_queues[execution_id]
            logger.info(f"执行SSE流结束: {execution_id}")
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@router.get("/status/{execution_id}")
async def get_execution_status(execution_id: str):
    """获取执行状态"""
    if execution_id not in execution_sessions:
        raise HTTPException(status_code=404, detail=f"执行会话 {execution_id} 不存在或已过期")
    
    session_info = execution_sessions[execution_id]
    
    return JSONResponse({
        "success": True,
        "data": {
            "execution_id": execution_id,
            "status": session_info["status"],
            "progress": session_info.get("progress", 0),
            "script_id": session_info["script_id"],
            "device_id": session_info["device_id"],
            "created_at": session_info["created_at"],
            "last_activity": session_info["last_activity"],
            "error": session_info.get("error"),
            "completed_at": session_info.get("completed_at")
        }
    })


@router.get("/results/{execution_id}")
async def get_execution_results(
    execution_id: str,
    session: AsyncSession = Depends(get_database)
):
    """获取执行结果"""
    try:
        if execution_id not in execution_sessions:
            raise HTTPException(status_code=404, detail="执行会话不存在")
        
        execution_record_id = execution_sessions[execution_id]["execution_record_id"]
        
        execution_repo = AndroidExecutionRepository()
        execution_record = await execution_repo.get_by_id(session, execution_record_id)
        
        if not execution_record:
            raise HTTPException(status_code=404, detail="执行记录不存在")
        
        return JSONResponse({
            "success": True,
            "data": {
                "execution_id": execution_id,
                "execution_status": execution_record.execution_status,
                "start_time": execution_record.start_time,
                "end_time": execution_record.end_time,
                "duration_seconds": execution_record.duration_seconds,
                "execution_result": execution_record.execution_result,
                "test_results": execution_record.test_results,
                "execution_logs": execution_record.execution_logs,
                "screenshots": execution_record.screenshots,
                "video_path": execution_record.video_path,
                "error_message": execution_record.error_message
            }
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行结果失败: {execution_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取执行结果失败: {str(e)}")


@router.post("/stop/{execution_id}")
async def stop_android_execution(execution_id: str):
    """停止Android脚本执行"""
    try:
        if execution_id not in execution_sessions:
            raise HTTPException(status_code=404, detail="执行会话不存在")
        
        # 更新执行状态
        execution_sessions[execution_id]["status"] = "cancelled"
        execution_sessions[execution_id]["cancelled_at"] = datetime.now().isoformat()
        
        # 发送停止消息
        await send_execution_message(execution_id, {
            "type": "cancelled",
            "message": "🛑 执行已被用户取消",
            "progress": execution_sessions[execution_id].get("progress", 0)
        })
        
        return JSONResponse({
            "success": True,
            "message": "执行已停止"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止执行失败: {execution_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"停止执行失败: {str(e)}")


@router.get("/history")
async def get_execution_history(
    session_id: Optional[str] = None,
    device_id: Optional[str] = None,
    status: Optional[str] = None,
    page: int = 1,
    page_size: int = 20,
    session: AsyncSession = Depends(get_database)
):
    """获取执行历史记录"""
    try:
        execution_repo = AndroidExecutionRepository()
        
        if session_id:
            executions = await execution_repo.get_by_session_id(session, session_id)
        else:
            # 获取所有执行记录
            from sqlalchemy import select, desc
            from app.database.models.android_analysis import AndroidExecution
            
            query = select(AndroidExecution).order_by(desc(AndroidExecution.created_at))
            
            if device_id:
                query = query.where(AndroidExecution.device_id == device_id)
            if status:
                query = query.where(AndroidExecution.execution_status == status)
            
            result = await session.execute(query.limit(page_size).offset((page - 1) * page_size))
            executions = result.scalars().all()
        
        return JSONResponse({
            "success": True,
            "data": [
                {
                    "id": str(execution.id),
                    "session_id": execution.session_id,
                    "script_id": execution.script_id,
                    "device_id": execution.device_id,
                    "execution_name": execution.execution_name,
                    "execution_status": execution.execution_status,
                    "start_time": execution.start_time,
                    "end_time": execution.end_time,
                    "duration_seconds": execution.duration_seconds,
                    "error_message": execution.error_message,
                    "created_at": execution.created_at.isoformat() if execution.created_at else None
                }
                for execution in executions
            ],
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": len(executions)
            }
        })
        
    except Exception as e:
        logger.error(f"获取执行历史记录失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取执行历史失败: {str(e)}")
