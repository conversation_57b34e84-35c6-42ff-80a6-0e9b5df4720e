"""
Android脚本管理API端点
提供Android自动化脚本的创建、管理和查询功能
"""
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.database.connection import get_database
from app.database.repositories.android_analysis_repository import AndroidScriptRepository, AndroidAnalysisRepository
from app.core.messages.android import AndroidScriptGenerationRequest, AndroidScriptType
from app.agents.android import AndroidScriptGeneratorAgent

router = APIRouter()


@router.post("/generate")
async def generate_android_script(
    request: AndroidScriptGenerationRequest,
    background_tasks: BackgroundTasks,
    session: AsyncSession = Depends(get_database)
):
    """
    生成Android自动化脚本
    
    Args:
        request: 脚本生成请求
        background_tasks: 后台任务管理器
        session: 数据库会话
    
    Returns:
        生成的脚本信息
    """
    try:
        logger.info(f"开始生成Android脚本: {request.session_id}")
        
        # 获取分析结果
        analysis_repo = AndroidAnalysisRepository()
        analysis_result = await analysis_repo.get_by_analysis_id(session, request.analysis_id)
        
        if not analysis_result:
            raise HTTPException(status_code=404, detail="分析结果不存在")
        
        # 创建脚本生成智能体
        script_generator = AndroidScriptGeneratorAgent()
        
        # 生成脚本
        generated_script = await script_generator.generate_script(
            analysis_result=analysis_result.to_dict(),
            script_type=request.script_type.value,
            script_language=request.script_language,
            test_description=request.test_description,
            session_id=request.session_id,
            include_setup=request.include_setup,
            include_teardown=request.include_teardown,
            use_page_object=request.use_page_object,
            add_assertions=request.add_assertions
        )
        
        # 保存脚本到数据库
        script_repo = AndroidScriptRepository()
        script_data = {
            "android_analysis_id": analysis_result.id,
            "session_id": request.session_id,
            "script_name": f"{request.test_description}_{request.script_type.value}_script",
            "script_type": request.script_type.value,
            "script_language": request.script_language,
            "script_content": generated_script["script_content"],
            "script_config": generated_script.get("script_config", {}),
            "test_description": request.test_description,
            "test_scenarios": generated_script.get("test_scenarios", []),
            "generated_by": "AndroidScriptGeneratorAgent",
            "generation_metadata": {
                "include_setup": request.include_setup,
                "include_teardown": request.include_teardown,
                "use_page_object": request.use_page_object,
                "add_assertions": request.add_assertions,
                "generation_time": datetime.now().isoformat()
            }
        }
        
        saved_script = await script_repo.create(session, **script_data)
        await session.commit()
        
        return JSONResponse({
            "success": True,
            "data": {
                "script_id": str(saved_script.id),
                "session_id": request.session_id,
                "analysis_id": request.analysis_id,
                "script_type": request.script_type.value,
                "script_language": request.script_language,
                "script_content": generated_script["script_content"],
                "script_config": generated_script.get("script_config", {}),
                "dependencies": generated_script.get("dependencies", []),
                "setup_instructions": generated_script.get("setup_instructions"),
                "execution_notes": generated_script.get("execution_notes")
            },
            "message": "Android脚本生成成功"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成Android脚本失败: {e}")
        raise HTTPException(status_code=500, detail=f"脚本生成失败: {str(e)}")


@router.get("/list")
async def list_android_scripts(
    session_id: Optional[str] = None,
    script_type: Optional[AndroidScriptType] = None,
    page: int = 1,
    page_size: int = 20,
    session: AsyncSession = Depends(get_database)
):
    """
    获取Android脚本列表
    
    Args:
        session_id: 会话ID（可选）
        script_type: 脚本类型（可选）
        page: 页码
        page_size: 页面大小
        session: 数据库会话
    
    Returns:
        脚本列表
    """
    try:
        script_repo = AndroidScriptRepository()
        
        if session_id:
            scripts = await script_repo.get_by_session_id(session, session_id)
        else:
            # 获取所有脚本
            from sqlalchemy import select, desc
            from app.database.models.android_analysis import AndroidScript
            
            query = select(AndroidScript).order_by(desc(AndroidScript.created_at))
            
            if script_type:
                query = query.where(AndroidScript.script_type == script_type.value)
            
            result = await session.execute(query.limit(page_size).offset((page - 1) * page_size))
            scripts = result.scalars().all()
        
        return JSONResponse({
            "success": True,
            "data": [
                {
                    "id": str(script.id),
                    "session_id": script.session_id,
                    "android_analysis_id": script.android_analysis_id,
                    "script_name": script.script_name,
                    "script_type": script.script_type,
                    "script_language": script.script_language,
                    "test_description": script.test_description,
                    "test_scenarios": script.test_scenarios,
                    "generated_by": script.generated_by,
                    "generation_metadata": script.generation_metadata,
                    "created_at": script.created_at.isoformat() if script.created_at else None,
                    "updated_at": script.updated_at.isoformat() if script.updated_at else None
                }
                for script in scripts
            ],
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": len(scripts)
            }
        })
        
    except Exception as e:
        logger.error(f"获取Android脚本列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取脚本列表失败: {str(e)}")


@router.get("/{script_id}")
async def get_android_script(
    script_id: str,
    session: AsyncSession = Depends(get_database)
):
    """
    获取Android脚本详情
    
    Args:
        script_id: 脚本ID
        session: 数据库会话
    
    Returns:
        脚本详情
    """
    try:
        script_repo = AndroidScriptRepository()
        script = await script_repo.get_by_id(session, script_id)
        
        if not script:
            raise HTTPException(status_code=404, detail="脚本不存在")
        
        return JSONResponse({
            "success": True,
            "data": {
                "id": str(script.id),
                "session_id": script.session_id,
                "android_analysis_id": script.android_analysis_id,
                "script_name": script.script_name,
                "script_type": script.script_type,
                "script_language": script.script_language,
                "script_content": script.script_content,
                "script_config": script.script_config,
                "test_description": script.test_description,
                "test_scenarios": script.test_scenarios,
                "generated_by": script.generated_by,
                "generation_metadata": script.generation_metadata,
                "created_at": script.created_at.isoformat() if script.created_at else None,
                "updated_at": script.updated_at.isoformat() if script.updated_at else None
            }
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Android脚本详情失败: {script_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取脚本详情失败: {str(e)}")


@router.put("/{script_id}")
async def update_android_script(
    script_id: str,
    script_name: Optional[str] = None,
    script_content: Optional[str] = None,
    script_config: Optional[Dict[str, Any]] = None,
    test_description: Optional[str] = None,
    session: AsyncSession = Depends(get_database)
):
    """
    更新Android脚本
    
    Args:
        script_id: 脚本ID
        script_name: 脚本名称
        script_content: 脚本内容
        script_config: 脚本配置
        test_description: 测试描述
        session: 数据库会话
    
    Returns:
        更新结果
    """
    try:
        script_repo = AndroidScriptRepository()
        script = await script_repo.get_by_id(session, script_id)
        
        if not script:
            raise HTTPException(status_code=404, detail="脚本不存在")
        
        # 更新脚本信息
        update_data = {}
        if script_name is not None:
            update_data["script_name"] = script_name
        if script_content is not None:
            update_data["script_content"] = script_content
        if script_config is not None:
            update_data["script_config"] = script_config
        if test_description is not None:
            update_data["test_description"] = test_description
        
        if update_data:
            updated_script = await script_repo.update(session, script_id, **update_data)
            await session.commit()
            
            return JSONResponse({
                "success": True,
                "data": {
                    "id": str(updated_script.id),
                    "script_name": updated_script.script_name,
                    "script_content": updated_script.script_content,
                    "script_config": updated_script.script_config,
                    "test_description": updated_script.test_description,
                    "updated_at": updated_script.updated_at.isoformat() if updated_script.updated_at else None
                },
                "message": "脚本更新成功"
            })
        else:
            return JSONResponse({
                "success": True,
                "message": "没有需要更新的内容"
            })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新Android脚本失败: {script_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新脚本失败: {str(e)}")


@router.delete("/{script_id}")
async def delete_android_script(
    script_id: str,
    session: AsyncSession = Depends(get_database)
):
    """
    删除Android脚本
    
    Args:
        script_id: 脚本ID
        session: 数据库会话
    
    Returns:
        删除结果
    """
    try:
        script_repo = AndroidScriptRepository()
        script = await script_repo.get_by_id(session, script_id)
        
        if not script:
            raise HTTPException(status_code=404, detail="脚本不存在")
        
        await script_repo.delete(session, script_id)
        await session.commit()
        
        return JSONResponse({
            "success": True,
            "message": "脚本删除成功"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除Android脚本失败: {script_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"删除脚本失败: {str(e)}")


@router.get("/analysis/{analysis_id}")
async def get_scripts_by_analysis(
    analysis_id: str,
    session: AsyncSession = Depends(get_database)
):
    """
    根据分析ID获取相关脚本
    
    Args:
        analysis_id: 分析ID
        session: 数据库会话
    
    Returns:
        脚本列表
    """
    try:
        script_repo = AndroidScriptRepository()
        scripts = await script_repo.get_by_analysis_id(session, analysis_id)
        
        return JSONResponse({
            "success": True,
            "data": [
                {
                    "id": str(script.id),
                    "script_name": script.script_name,
                    "script_type": script.script_type,
                    "script_language": script.script_language,
                    "test_description": script.test_description,
                    "created_at": script.created_at.isoformat() if script.created_at else None
                }
                for script in scripts
            ]
        })
        
    except Exception as e:
        logger.error(f"根据分析ID获取脚本失败: {analysis_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取脚本失败: {str(e)}")


@router.post("/{script_id}/duplicate")
async def duplicate_android_script(
    script_id: str,
    new_name: Optional[str] = None,
    session: AsyncSession = Depends(get_database)
):
    """
    复制Android脚本
    
    Args:
        script_id: 原脚本ID
        new_name: 新脚本名称
        session: 数据库会话
    
    Returns:
        新脚本信息
    """
    try:
        script_repo = AndroidScriptRepository()
        original_script = await script_repo.get_by_id(session, script_id)
        
        if not original_script:
            raise HTTPException(status_code=404, detail="原脚本不存在")
        
        # 创建新脚本
        new_script_data = {
            "android_analysis_id": original_script.android_analysis_id,
            "session_id": original_script.session_id,
            "script_name": new_name or f"{original_script.script_name}_copy",
            "script_type": original_script.script_type,
            "script_language": original_script.script_language,
            "script_content": original_script.script_content,
            "script_config": original_script.script_config,
            "test_description": original_script.test_description,
            "test_scenarios": original_script.test_scenarios,
            "generated_by": "Copy",
            "generation_metadata": {
                "original_script_id": str(original_script.id),
                "copy_time": datetime.now().isoformat()
            }
        }
        
        new_script = await script_repo.create(session, **new_script_data)
        await session.commit()
        
        return JSONResponse({
            "success": True,
            "data": {
                "id": str(new_script.id),
                "script_name": new_script.script_name,
                "original_script_id": script_id
            },
            "message": "脚本复制成功"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"复制Android脚本失败: {script_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"复制脚本失败: {str(e)}")
