"""
Android界面分析API端点
提供Android应用界面分析、元素识别和测试脚本生成功能
"""
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Request, UploadFile, File
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.database.connection import get_database
from app.database.repositories.android_analysis_repository import AndroidAnalysisRepository
from app.core.messages.android import (
    AndroidAnalysisRequest, 
    AndroidAnalysisResponse,
    AndroidScriptGenerationRequest,
    AndroidAnalysisResult
)
from app.agents.android import AndroidAnalyzerAgent, AndroidScriptGeneratorAgent, AndroidDataStorageAgent
from app.agents.factory import AgentFactory
from app.core.types import AgentTypes
from app.utils.file_utils import save_uploaded_file, ensure_directories
from app.utils.image_utils import encode_image_to_base64

router = APIRouter()

# 全局会话管理
active_sessions: Dict[str, Dict[str, Any]] = {}
message_queues: Dict[str, asyncio.Queue] = {}


@router.post("/analyze")
async def analyze_android_interface(
    request: AndroidAnalysisRequest,
    background_tasks: BackgroundTasks,
    session: AsyncSession = Depends(get_database)
):
    """
    分析Android界面
    
    Args:
        request: Android分析请求
        background_tasks: 后台任务管理器
        session: 数据库会话
    
    Returns:
        分析会话信息
    """
    try:
        session_id = request.session_id or str(uuid.uuid4())
        
        logger.info(f"开始Android界面分析: {session_id}")
        
        # 创建会话信息
        session_info = {
            "session_id": session_id,
            "status": "processing",
            "created_at": datetime.now().isoformat(),
            "last_activity": datetime.now().isoformat(),
            "request_data": request.dict(),
            "progress": 0,
            "app_info": {
                "package_name": request.package_name,
                "activity_name": request.activity_name,
                "device_id": request.device_id
            }
        }
        
        # 存储会话信息
        active_sessions[session_id] = session_info
        
        # 创建消息队列
        message_queue = asyncio.Queue()
        message_queues[session_id] = message_queue
        
        # 启动后台分析任务
        background_tasks.add_task(
            process_android_analysis,
            session_id,
            request,
            session
        )
        
        return JSONResponse({
            "success": True,
            "session_id": session_id,
            "message": "Android界面分析已启动",
            "stream_url": f"/api/v1/android/analysis/stream/{session_id}"
        })
        
    except Exception as e:
        logger.error(f"启动Android界面分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动分析失败: {str(e)}")


async def process_android_analysis(
    session_id: str,
    request: AndroidAnalysisRequest,
    db_session: AsyncSession
):
    """处理Android界面分析的后台任务"""
    try:
        logger.info(f"开始处理Android界面分析: {session_id}")
        
        # 更新会话状态
        if session_id in active_sessions:
            active_sessions[session_id]["status"] = "analyzing"
            active_sessions[session_id]["progress"] = 10
        
        # 发送初始消息
        await send_message_to_queue(session_id, {
            "type": "progress",
            "message": "🚀 开始Android界面分析...",
            "progress": 10
        })
        
        # 创建Android分析智能体
        agent_factory = AgentFactory()
        analyzer_agent = agent_factory.create_agent(AgentTypes.ANDROID_ANALYZER.value)
        
        # 执行界面分析
        await send_message_to_queue(session_id, {
            "type": "progress", 
            "message": "🔍 正在分析Android界面元素...",
            "progress": 30
        })
        
        analysis_result = await analyzer_agent.analyze_android_interface(
            screenshot_data=request.screenshot_data,
            ui_hierarchy=request.ui_hierarchy,
            test_description=request.test_description,
            additional_context=request.additional_context,
            session_id=session_id
        )
        
        # 更新进度
        await send_message_to_queue(session_id, {
            "type": "progress",
            "message": "💾 正在保存分析结果...",
            "progress": 70
        })
        
        # 保存分析结果到数据库
        storage_agent = AndroidDataStorageAgent()
        await storage_agent.save_analysis_result(
            session_id=session_id,
            analysis_result=analysis_result,
            request_data=request.dict()
        )
        
        # 生成脚本（如果需要）
        if request.generate_formats:
            await send_message_to_queue(session_id, {
                "type": "progress",
                "message": "📝 正在生成自动化脚本...",
                "progress": 85
            })
            
            script_generator = AndroidScriptGeneratorAgent()
            generated_scripts = []
            
            for script_format in request.generate_formats:
                script_result = await script_generator.generate_script(
                    analysis_result=analysis_result,
                    script_type=script_format,
                    test_description=request.test_description,
                    session_id=session_id
                )
                generated_scripts.append(script_result)
        
        # 完成分析
        await send_message_to_queue(session_id, {
            "type": "complete",
            "message": "✅ Android界面分析完成！",
            "progress": 100,
            "result": {
                "analysis_result": analysis_result,
                "generated_scripts": generated_scripts if request.generate_formats else []
            }
        })
        
        # 更新会话状态
        if session_id in active_sessions:
            active_sessions[session_id]["status"] = "completed"
            active_sessions[session_id]["progress"] = 100
            active_sessions[session_id]["completed_at"] = datetime.now().isoformat()
            active_sessions[session_id]["result"] = analysis_result
        
        logger.info(f"Android界面分析完成: {session_id}")
        
    except Exception as e:
        logger.error(f"Android界面分析失败: {session_id}, 错误: {e}")
        
        # 发送错误消息
        await send_message_to_queue(session_id, {
            "type": "error",
            "message": f"❌ 分析失败: {str(e)}",
            "error": str(e)
        })
        
        # 更新会话状态
        if session_id in active_sessions:
            active_sessions[session_id]["status"] = "failed"
            active_sessions[session_id]["error"] = str(e)


async def send_message_to_queue(session_id: str, message: Dict[str, Any]):
    """发送消息到队列"""
    if session_id in message_queues:
        try:
            await message_queues[session_id].put(message)
        except Exception as e:
            logger.error(f"发送消息到队列失败: {session_id}, 错误: {e}")


@router.get("/stream/{session_id}")
async def stream_android_analysis(session_id: str, request: Request):
    """
    Android分析SSE流式端点
    
    Args:
        session_id: 会话ID
        request: HTTP请求对象
    
    Returns:
        EventSourceResponse: SSE响应流
    """
    # 验证会话是否存在
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail=f"会话 {session_id} 不存在或已过期")
    
    logger.info(f"开始Android分析SSE流: {session_id}")
    
    # 确保消息队列存在
    if session_id not in message_queues:
        message_queue = asyncio.Queue()
        message_queues[session_id] = message_queue
    else:
        message_queue = message_queues[session_id]
    
    async def event_generator():
        try:
            while True:
                # 检查客户端是否断开连接
                if await request.is_disconnected():
                    logger.info(f"客户端断开连接: {session_id}")
                    break
                
                try:
                    # 等待消息，设置超时避免无限等待
                    message = await asyncio.wait_for(message_queue.get(), timeout=1.0)
                    
                    # 发送消息
                    yield f"data: {message}\n\n"
                    
                    # 如果是完成或错误消息，结束流
                    if isinstance(message, dict) and message.get("type") in ["complete", "error"]:
                        break
                        
                except asyncio.TimeoutError:
                    # 发送心跳消息
                    yield f"data: {{'type': 'heartbeat', 'timestamp': '{datetime.now().isoformat()}'}}\n\n"
                    continue
                    
        except Exception as e:
            logger.error(f"SSE流错误: {session_id}, 错误: {e}")
            yield f"data: {{'type': 'error', 'message': '流连接错误: {str(e)}'}}\n\n"
        
        finally:
            # 清理资源
            if session_id in message_queues:
                del message_queues[session_id]
            logger.info(f"SSE流结束: {session_id}")
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@router.get("/status/{session_id}")
async def get_analysis_status(session_id: str):
    """获取分析状态"""
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail=f"会话 {session_id} 不存在或已过期")
    
    session_info = active_sessions[session_id]
    
    return JSONResponse({
        "success": True,
        "data": {
            "session_id": session_id,
            "status": session_info["status"],
            "progress": session_info.get("progress", 0),
            "created_at": session_info["created_at"],
            "last_activity": session_info["last_activity"],
            "error": session_info.get("error"),
            "completed_at": session_info.get("completed_at"),
            "app_info": session_info["app_info"]
        }
    })


@router.get("/results/{session_id}")
async def get_analysis_results(
    session_id: str,
    session: AsyncSession = Depends(get_database)
):
    """获取分析结果"""
    try:
        repo = AndroidAnalysisRepository()
        results = await repo.get_by_session_id(session, session_id)
        
        if not results:
            raise HTTPException(status_code=404, detail="未找到分析结果")
        
        return JSONResponse({
            "success": True,
            "data": [result.to_dict() for result in results]
        })
        
    except Exception as e:
        logger.error(f"获取分析结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取结果失败: {str(e)}")


@router.get("/apps")
async def get_android_apps(
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    session: AsyncSession = Depends(get_database)
):
    """获取Android应用列表"""
    try:
        repo = AndroidAnalysisRepository()
        
        if search:
            apps = await repo.search_by_app_name(session, search, limit=page_size)
        else:
            # 获取所有应用
            from sqlalchemy import select, desc
            from app.database.models.android_analysis import AndroidAnalysisResult
            
            query = select(AndroidAnalysisResult).order_by(desc(AndroidAnalysisResult.created_at))
            result = await session.execute(query.limit(page_size).offset((page - 1) * page_size))
            apps = result.scalars().all()
        
        return JSONResponse({
            "success": True,
            "data": [app.to_dict() for app in apps],
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": len(apps)
            }
        })
        
    except Exception as e:
        logger.error(f"获取Android应用列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取应用列表失败: {str(e)}")


@router.post("/upload-screenshot")
async def upload_android_screenshot(
    file: UploadFile = File(...),
    device_id: Optional[str] = None,
    package_name: Optional[str] = None
):
    """上传Android截图"""
    try:
        # 确保上传目录存在
        ensure_directories(["uploads/android/screenshots"])
        
        # 保存上传的文件
        file_path = await save_uploaded_file(file, "uploads/android/screenshots")
        
        # 编码为base64
        screenshot_data = encode_image_to_base64(file_path)
        
        return JSONResponse({
            "success": True,
            "data": {
                "file_path": file_path,
                "screenshot_data": screenshot_data,
                "device_id": device_id,
                "package_name": package_name
            },
            "message": "截图上传成功"
        })
        
    except Exception as e:
        logger.error(f"上传Android截图失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")
