# UI自动化测试系统环境配置示例
# 复制此文件为 .env 并填入实际配置值

# ============ 应用基础配置 ============
APP_NAME="UI自动化测试系统"
APP_VERSION="1.0.0"
DEBUG=true
API_V1_STR="/api/v1"

# ============ 服务器配置 ============
HOST="0.0.0.0"
PORT=8000
RELOAD=true

# ============ 安全配置 ============
SECRET_KEY="your-super-secret-key-change-this-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=10080
ALGORITHM="HS256"

# ============ CORS配置 ============
BACKEND_CORS_ORIGINS="http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000,http://127.0.0.1:3001"

# ============ MySQL数据库配置 ============
DATABASE_URL=mysql+aiomysql://root:root@***************:3306/ai_automation
MYSQL_HOST=***************
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=root
MYSQL_DATABASE=ai_automation

# ============ Neo4j图数据库配置 ============
NEO4J_URI="bolt://***************:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="myverystrongpassword123!"

# ============ Milvus向量数据库配置 ============
MILVUS_HOST="***************"
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME="ui_automation_vectors"

# ============ Redis配置 ============
REDIS_HOST="***************"
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=""

# ============ AI模型配置 ============

# DeepSeek配置
DEEPSEEK_API_KEY="sk_73a4950b1fe27d5d73b61b33a88aa2870474b9d81c857d6331e60a0"
DEEPSEEK_BASE_URL="https://hk-intra-paas.transsion.com/tranai-proxy/v1"
DEEPSEEK_MODEL="tran-ai/deepseek-r1"

# UI-TARS配置
UI_TARS_API_KEY="7cd14776-e901-4875-868d-e01ee77a4eb2"
UI_TARS_BASE_URL="https://ark.cn-beijing.volces.com/api/v3"
UI_TARS_MODEL="doubao-1-5-ui-tars-250428"
UI_TARS_ENDPOINT_URL="your_huggingface_endpoint_url_here"

# Qwen-VL配置
QWEN_VL_API_KEY="sk_73a4950b1fe27d5d73b61b33a88aa2870474b9d81c857d6331e60a0"
QWEN_VL_BASE_URL="https://hk-intra-paas.transsion.com/tranai-proxy/v1"
QWEN_VL_MODEL="qwen-vl-max-latest"



# OpenAI配置（备用）
OPENAI_API_KEY="your_openai_api_key_here"
OPENAI_BASE_URL="https://api.openai.com/v1"

# ============ 文件存储配置 ============
UPLOAD_DIR="uploads"
MAX_FILE_SIZE=104857600
ALLOWED_EXTENSIONS=".pdf,.doc,.docx,.txt,.md,.yaml,.yml"

# ============ MidScene.js配置 ============
MIDSCENE_SERVICE_URL="http://localhost:3002"
MIDSCENE_TIMEOUT=300

# ============ Playwright配置 ============
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000
PLAYWRIGHT_VIEWPORT_WIDTH=1280
PLAYWRIGHT_VIEWPORT_HEIGHT=960

# ============ AutoGen配置 ============
AUTOGEN_CACHE_ENABLED=true
AUTOGEN_MAX_ROUND=10
AUTOGEN_TIMEOUT=600

# ============ 日志配置 ============
LOG_LEVEL="INFO"
LOG_FILE="logs/app.log"
LOG_ROTATION="1 day"
LOG_RETENTION="30 days"

# ============ 监控配置 ============
ENABLE_METRICS=true
METRICS_PORT=8001

# ============ 功能开关 ============
ENABLE_CACHING=true
ENABLE_RATE_LIMITING=true
ENABLE_MONITORING=true
ENABLE_ASYNC_PROCESSING=true

# ============ 混合检索配置 ============
HYBRID_RETRIEVAL_ENABLED=true
VECTOR_SEARCH_TOP_K=10
SIMILARITY_THRESHOLD=0.7
