2025-08-20 16:45:28 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:29 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:30 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:33 | ERROR    | app.api.v1.endpoints.web.test_execution_reports:list_reports:250 | 获取测试报告列表失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:33 | ERROR    | app.api.v1.endpoints.web.test_execution_reports:get_report_stats:340 | 获取统计信息失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 17:27:44 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(test_scripts.id) AS count_1 
FROM test_scripts]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
