2025-08-20 16:08:48 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:08:48 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:08:52 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:08:52 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:09:18 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:09:24 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:09:24 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:09:25 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:09:25 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:14:19 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:14:23 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:14:23 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:14:24 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:14:24 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:47 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:20:51 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:51 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:52 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:52 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:56 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:56 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:57 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:57 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:27:09 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:44:27 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:45:28 | ERROR    | app.database.repositories.script_repository:get_statistics:198 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:28 | ERROR    | app.services.database_script_service:get_script_statistics:218 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:28 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:29 | ERROR    | app.database.repositories.script_repository:get_statistics:198 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:29 | ERROR    | app.services.database_script_service:get_script_statistics:218 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:29 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:30 | ERROR    | app.database.repositories.script_repository:get_statistics:198 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:30 | ERROR    | app.services.database_script_service:get_script_statistics:218 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:30 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:31 | ERROR    | app.services.execution_history_service:get_execution_history:112 | 获取执行历史失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:31 | ERROR    | app.services.execution_history_service:get_execution_history:112 | 获取执行历史失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:33 | ERROR    | app.api.v1.endpoints.web.test_execution_reports:list_reports:250 | 获取测试报告列表失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:33 | ERROR    | app.api.v1.endpoints.web.test_execution_reports:get_report_stats:340 | 获取统计信息失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 17:02:24 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 17:27:44 | ERROR    | app.database.repositories.script_repository:get_statistics:198 | 获取脚本统计失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(test_scripts.id) AS count_1 
FROM test_scripts]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 17:27:44 | ERROR    | app.services.database_script_service:get_script_statistics:218 | 获取脚本统计失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(test_scripts.id) AS count_1 
FROM test_scripts]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 17:27:44 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(test_scripts.id) AS count_1 
FROM test_scripts]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 18:22:32 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '***************'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 18:22:32 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '***************'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 18:22:36 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '***************'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 18:22:36 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '***************'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 18:25:24 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 18:27:49 | ERROR    | app.agents.factory:create_agent:180 | 创建智能体失败 (image_description_generator): RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 18:58:55 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 19:27:49 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
