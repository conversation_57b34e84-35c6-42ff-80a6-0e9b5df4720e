2025-08-20 16:08:44 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:08:44 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:08:44 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:08:44 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:08:44 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:08:44 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:08:44 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:08:44 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:08:44 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:08:44 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:08:44 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:08:44 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:08:44 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:08:44 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:08:44 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: localhost:3306/ai_automation
2025-08-20 16:08:44 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:08:48 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:08:48 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:08:48 | WARNING  | app.core.database_startup:initialize_database_on_startup:47 | ⚠️ 将回退到文件存储模式
2025-08-20 16:08:48 | WARNING  | app.main:init_databases:173 | ⚠️ 主数据库初始化失败，将使用文件存储
2025-08-20 16:08:48 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:08:48 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:08:48 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:08:48 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:08:52 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:08:52 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:08:52 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:08:52 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:09:18 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:09:18 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:09:18 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:09:18 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:09:18 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:09:23 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:09:23 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:09:23 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:09:23 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:09:23 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:09:23 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:09:23 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:09:23 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:09:23 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:09:23 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:09:23 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:09:23 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:09:23 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:09:23 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:09:23 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:09:23 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:09:24 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:09:24 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:09:24 | WARNING  | app.core.database_startup:initialize_database_on_startup:47 | ⚠️ 将回退到文件存储模式
2025-08-20 16:09:24 | WARNING  | app.main:init_databases:173 | ⚠️ 主数据库初始化失败，将使用文件存储
2025-08-20 16:09:24 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:09:24 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:09:24 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:09:24 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:09:25 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:09:25 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:09:25 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:09:25 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:14:19 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:14:19 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:14:19 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:14:19 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:14:19 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:14:22 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:14:22 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:14:22 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:14:22 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:14:22 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:14:22 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:14:22 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:14:22 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:14:22 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:14:22 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:14:22 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:14:22 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:14:22 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:14:22 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:14:22 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:14:22 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:14:23 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:14:23 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:14:23 | WARNING  | app.core.database_startup:initialize_database_on_startup:47 | ⚠️ 将回退到文件存储模式
2025-08-20 16:14:23 | WARNING  | app.main:init_databases:173 | ⚠️ 主数据库初始化失败，将使用文件存储
2025-08-20 16:14:23 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:14:23 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:14:23 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:14:23 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:14:24 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:14:24 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:14:24 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:14:24 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:20:47 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:20:47 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:20:47 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:20:47 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:20:47 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:20:50 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:20:50 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:20:50 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:20:50 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:20:50 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:20:50 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:20:50 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:20:50 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:20:50 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:20:50 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:20:50 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:20:50 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:20:50 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:20:50 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:20:50 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:20:50 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:20:51 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:51 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:51 | WARNING  | app.core.database_startup:initialize_database_on_startup:47 | ⚠️ 将回退到文件存储模式
2025-08-20 16:20:51 | WARNING  | app.main:init_databases:173 | ⚠️ 主数据库初始化失败，将使用文件存储
2025-08-20 16:20:51 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:20:51 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:20:51 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:20:51 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:20:52 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:52 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:52 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:20:52 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:20:55 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:20:55 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:20:55 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:20:55 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:20:55 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:20:55 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:20:55 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:20:55 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:20:55 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:20:55 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:20:55 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:20:55 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:20:55 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:20:55 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:20:55 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:20:55 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:20:56 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:56 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:56 | WARNING  | app.core.database_startup:initialize_database_on_startup:47 | ⚠️ 将回退到文件存储模式
2025-08-20 16:20:56 | WARNING  | app.main:init_databases:173 | ⚠️ 主数据库初始化失败，将使用文件存储
2025-08-20 16:20:56 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:20:56 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:20:56 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:20:56 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:20:57 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:57 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:20:57 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:20:57 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:27:09 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:27:09 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:27:09 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:27:09 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:27:09 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:31:52 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 16:31:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 16:31:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 16:31:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 16:31:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 16:31:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 16:31:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 16:31:52 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 16:31:52 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 16:31:52 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:31:52 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 16:31:52 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 16:31:52 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 16:31:52 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 16:31:52 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 16:31:52 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 16:32:00 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 16:32:01 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 16:32:01 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 16:32:01 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 16:32:01 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 16:32:01 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 16:32:01 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 16:32:01 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 16:32:02 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 16:32:02 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 16:32:02 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 16:44:27 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 16:44:27 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 16:44:27 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 16:44:27 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 16:44:27 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 16:45:28 | ERROR    | app.database.repositories.script_repository:get_statistics:198 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:28 | ERROR    | app.services.database_script_service:get_script_statistics:218 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:28 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:29 | ERROR    | app.database.repositories.script_repository:get_statistics:198 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:29 | ERROR    | app.services.database_script_service:get_script_statistics:218 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:29 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:30 | ERROR    | app.database.repositories.script_repository:get_statistics:198 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:30 | ERROR    | app.services.database_script_service:get_script_statistics:218 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:30 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:31 | ERROR    | app.services.execution_history_service:get_execution_history:112 | 获取执行历史失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:31 | ERROR    | app.services.execution_history_service:get_execution_history:112 | 获取执行历史失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:33 | ERROR    | app.api.v1.endpoints.web.test_execution_reports:list_reports:250 | 获取测试报告列表失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 16:45:33 | ERROR    | app.api.v1.endpoints.web.test_execution_reports:get_report_stats:340 | 获取统计信息失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'**************' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 17:02:24 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 17:02:24 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 17:02:24 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 17:02:24 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 17:02:24 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 17:02:33 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 17:02:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 17:02:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 17:02:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 17:02:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 17:02:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 17:02:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 17:02:33 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 17:02:33 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 17:02:33 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 17:02:33 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 17:02:33 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 17:02:33 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 17:02:33 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 17:02:33 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 17:02:33 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 17:02:43 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 17:02:43 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 17:02:44 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 17:02:44 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 17:02:44 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 17:02:44 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 17:02:45 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 17:02:45 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 17:02:45 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 17:02:46 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 17:02:46 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 17:27:44 | ERROR    | app.database.repositories.script_repository:get_statistics:198 | 获取脚本统计失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(test_scripts.id) AS count_1 
FROM test_scripts]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 17:27:44 | ERROR    | app.services.database_script_service:get_script_statistics:218 | 获取脚本统计失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(test_scripts.id) AS count_1 
FROM test_scripts]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 17:27:44 | ERROR    | app.api.v1.endpoints.web.test_script_management:get_script_statistics:118 | 获取脚本统计失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(test_scripts.id) AS count_1 
FROM test_scripts]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 18:22:25 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 18:22:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 18:22:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 18:22:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 18:22:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 18:22:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 18:22:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 18:22:25 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 18:22:25 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 18:22:25 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:22:25 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:22:25 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 18:22:25 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 18:22:25 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 18:22:25 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 18:22:26 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 18:22:32 | ERROR    | app.database.connection:create_tables:88 | 数据库表创建失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '155.138.202.224'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 18:22:32 | ERROR    | app.core.database_startup:initialize_database_on_startup:46 | ❌ 数据库初始化失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '155.138.202.224'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 18:22:32 | WARNING  | app.core.database_startup:initialize_database_on_startup:47 | ⚠️ 将回退到文件存储模式
2025-08-20 18:22:32 | WARNING  | app.main:init_databases:173 | ⚠️ 主数据库初始化失败，将使用文件存储
2025-08-20 18:22:32 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 18:22:32 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 18:22:33 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 18:22:33 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 18:22:36 | ERROR    | app.database.repositories.scheduled_task_repository:get_active_tasks:203 | 获取活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '155.138.202.224'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 18:22:36 | ERROR    | app.services.task_scheduler_service:_load_active_tasks:371 | 加载活跃任务失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '155.138.202.224'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-20 18:22:36 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 18:22:36 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 18:25:24 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 18:25:24 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 18:25:24 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 18:25:24 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 18:25:24 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 18:25:29 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 18:25:29 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 18:25:29 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 18:25:29 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 18:25:29 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 18:25:29 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 18:25:29 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 18:25:29 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 18:25:29 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 18:25:29 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:25:29 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:25:29 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 18:25:29 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 18:25:29 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 18:25:29 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 18:25:29 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 18:25:39 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 18:25:39 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 18:25:40 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 18:25:40 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 18:25:40 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 18:25:40 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 18:25:40 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 18:25:40 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 18:25:40 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 18:25:41 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 18:25:41 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 18:27:49 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-08-20 18:27:49 | DEBUG    | app.core.agents.collector:set_callback:47 | 设置流式响应回调函数
2025-08-20 18:27:49 | INFO     | app.services.web.orchestrator_service:__init__:31 | Web智能体编排器初始化成功
2025-08-20 18:27:49 | INFO     | app.services.web.orchestrator_service:generate_description_from_image:447 | 开始图片到描述生成工作流: b13e4490-b629-4ba7-b7a0-3bd48afad0b9
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_web_agents:240 | 开始注册Web平台智能体...
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:197 | 尝试注册智能体: page_analyzer
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:198 | 可用的智能体类型: ['page_analyzer', 'page_analysis_storage', 'yaml_generator', 'yaml_executor', 'playwright_generator', 'playwright_executor', 'script_database_saver', 'image_description_generator', 'test_case_element_parser', 'android_analyzer', 'android_script_generator', 'android_executor', 'android_device_manager', 'android_data_storage']
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_agent:222 | 注册智能体成功: 页面分析智能体
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:197 | 尝试注册智能体: image_description_generator
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:198 | 可用的智能体类型: ['page_analyzer', 'page_analysis_storage', 'yaml_generator', 'yaml_executor', 'playwright_generator', 'playwright_executor', 'script_database_saver', 'image_description_generator', 'test_case_element_parser', 'android_analyzer', 'android_script_generator', 'android_executor', 'android_device_manager', 'android_data_storage']
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_agent:222 | 注册智能体成功: image_description_generator
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:197 | 尝试注册智能体: page_analysis_storage
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:198 | 可用的智能体类型: ['page_analyzer', 'page_analysis_storage', 'yaml_generator', 'yaml_executor', 'playwright_generator', 'playwright_executor', 'script_database_saver', 'image_description_generator', 'test_case_element_parser', 'android_analyzer', 'android_script_generator', 'android_executor', 'android_device_manager', 'android_data_storage']
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_agent:222 | 注册智能体成功: 页面分析存储智能体
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:197 | 尝试注册智能体: yaml_generator
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:198 | 可用的智能体类型: ['page_analyzer', 'page_analysis_storage', 'yaml_generator', 'yaml_executor', 'playwright_generator', 'playwright_executor', 'script_database_saver', 'image_description_generator', 'test_case_element_parser', 'android_analyzer', 'android_script_generator', 'android_executor', 'android_device_manager', 'android_data_storage']
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_agent:222 | 注册智能体成功: YAML生成智能体
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:197 | 尝试注册智能体: yaml_executor
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:198 | 可用的智能体类型: ['page_analyzer', 'page_analysis_storage', 'yaml_generator', 'yaml_executor', 'playwright_generator', 'playwright_executor', 'script_database_saver', 'image_description_generator', 'test_case_element_parser', 'android_analyzer', 'android_script_generator', 'android_executor', 'android_device_manager', 'android_data_storage']
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_agent:222 | 注册智能体成功: YAML脚本执行智能体
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:197 | 尝试注册智能体: playwright_generator
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:198 | 可用的智能体类型: ['page_analyzer', 'page_analysis_storage', 'yaml_generator', 'yaml_executor', 'playwright_generator', 'playwright_executor', 'script_database_saver', 'image_description_generator', 'test_case_element_parser', 'android_analyzer', 'android_script_generator', 'android_executor', 'android_device_manager', 'android_data_storage']
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_agent:222 | 注册智能体成功: Playwright代码生成智能体
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:197 | 尝试注册智能体: playwright_executor
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:198 | 可用的智能体类型: ['page_analyzer', 'page_analysis_storage', 'yaml_generator', 'yaml_executor', 'playwright_generator', 'playwright_executor', 'script_database_saver', 'image_description_generator', 'test_case_element_parser', 'android_analyzer', 'android_script_generator', 'android_executor', 'android_device_manager', 'android_data_storage']
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_agent:222 | 注册智能体成功: Playwright执行智能体
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:197 | 尝试注册智能体: script_database_saver
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:198 | 可用的智能体类型: ['page_analyzer', 'page_analysis_storage', 'yaml_generator', 'yaml_executor', 'playwright_generator', 'playwright_executor', 'script_database_saver', 'image_description_generator', 'test_case_element_parser', 'android_analyzer', 'android_script_generator', 'android_executor', 'android_device_manager', 'android_data_storage']
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_agent:222 | 注册智能体成功: 脚本数据库保存智能体
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:197 | 尝试注册智能体: test_case_element_parser
2025-08-20 18:27:49 | DEBUG    | app.agents.factory:register_agent:198 | 可用的智能体类型: ['page_analyzer', 'page_analysis_storage', 'yaml_generator', 'yaml_executor', 'playwright_generator', 'playwright_executor', 'script_database_saver', 'image_description_generator', 'test_case_element_parser', 'android_analyzer', 'android_script_generator', 'android_executor', 'android_device_manager', 'android_data_storage']
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_agent:222 | 注册智能体成功: 测试用例元素解析智能体
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_web_agents:319 | Web平台智能体注册完成，共注册 9 个智能体
2025-08-20 18:27:49 | INFO     | app.agents.factory:register_stream_collector:409 | 流式响应收集器注册成功
2025-08-20 18:27:49 | INFO     | app.services.web.orchestrator_service:_initialize_runtime:86 | 运行时初始化成功，已注册 9 个智能体: b13e4490-b629-4ba7-b7a0-3bd48afad0b9
2025-08-20 18:27:49 | INFO     | app.services.web.orchestrator_service:generate_description_from_image:468 | 图片到描述生成工作流完成: b13e4490-b629-4ba7-b7a0-3bd48afad0b9
2025-08-20 18:27:49 | ERROR    | app.agents.factory:create_agent:180 | 创建智能体失败 (image_description_generator): RoutedAgent.__init__() got an unexpected keyword argument 'agent_id'
2025-08-20 18:27:49 | INFO     | app.agents.factory:clear_registered_agents:453 | 已清空智能体注册记录
2025-08-20 18:27:49 | DEBUG    | app.services.web.orchestrator_service:_cleanup_runtime:144 | 运行时清理成功完成
2025-08-20 18:56:20 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 18:56:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads
2025-08-20 18:56:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/images
2025-08-20 18:56:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/yaml
2025-08-20 18:56:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: uploads/playwright
2025-08-20 18:56:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: logs
2025-08-20 18:56:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: static
2025-08-20 18:56:20 | INFO     | app.utils.file_utils:ensure_directories:35 | 确保目录存在: screenshots
2025-08-20 18:56:20 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 18:56:20 | INFO     | app.core.llms:get_model_config_status:109 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:56:20 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 18:56:20 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 18:56:20 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 18:56:20 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 18:56:20 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 155.138.202.224:3306/ai_automation
2025-08-20 18:56:20 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-20 18:56:31 | INFO     | app.database.connection:create_tables:86 | 数据库表创建成功
2025-08-20 18:56:31 | INFO     | app.core.database_startup:initialize_database_on_startup:37 | ✅ 数据库连接验证成功
2025-08-20 18:56:32 | INFO     | app.core.database_startup:initialize_database_on_startup:42 | 🎉 数据库初始化完成
2025-08-20 18:56:32 | INFO     | app.main:init_databases:171 | ✅ 主数据库连接初始化完成
2025-08-20 18:56:32 | INFO     | app.main:init_databases:179 | ✅ 数据库连接初始化完成
2025-08-20 18:56:32 | INFO     | app.main:warmup_ai_models:190 | 预热AI模型...
2025-08-20 18:56:32 | INFO     | app.main:warmup_ai_models:201 | ✅ AI模型预热完成
2025-08-20 18:56:32 | INFO     | app.services.task_scheduler_service:initialize:59 | 定时任务调度器启动成功
2025-08-20 18:56:33 | INFO     | app.services.task_scheduler_service:_load_active_tasks:368 | 已加载 0 个活跃定时任务
2025-08-20 18:56:33 | INFO     | app.main:init_task_scheduler:261 | ✅ 定时任务调度器初始化完成
2025-08-20 18:56:33 | INFO     | app.main:lifespan:47 | ✅ 系统启动完成
2025-08-20 18:58:55 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 18:58:55 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 18:58:55 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 18:58:55 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 18:58:55 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
2025-08-20 19:27:49 | INFO     | app.main:lifespan:52 | 🔄 系统关闭中...
2025-08-20 19:27:49 | ERROR    | app.main:cleanup_resources:293 | 资源清理失败: 'DatabaseManager' object has no attribute 'shutdown'
2025-08-20 19:27:49 | INFO     | app.services.task_scheduler_service:shutdown:73 | 定时任务调度器已关闭
2025-08-20 19:27:49 | INFO     | app.main:shutdown_task_scheduler:271 | ✅ 定时任务调度器关闭完成
2025-08-20 19:27:49 | INFO     | app.main:lifespan:60 | ✅ 系统关闭完成
