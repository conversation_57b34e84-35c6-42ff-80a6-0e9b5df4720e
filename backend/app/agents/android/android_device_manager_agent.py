"""
Android设备管理智能体
负责管理Android设备连接、监控设备状态和设备操作
支持真机、模拟器、设备农场等多种设备类型
"""
import json
import uuid
import asyncio
import subprocess
from typing import Dict, List, Any, Optional
from datetime import datetime

try:
    from autogen_core import message_handler, MessageContext
    AUTOGEN_AVAILABLE = True
except ImportError:
    # 如果autogen不可用，使用占位符
    AUTOGEN_AVAILABLE = False
    def message_handler(func):
        return func
    def type_subscription(topic_type):
        def decorator(cls):
            return cls
        return decorator
    class MessageContext:
        pass

from loguru import logger

from app.core.messages.android import AndroidDeviceConnectionMessage
from app.core.agents.base import BaseAgent
from app.core.types import TopicTypes, AgentTypes, AGENT_NAMES, AgentPlatform


class AndroidDeviceManagerAgent:
    """Android设备管理智能体，负责设备连接和状态管理"""

    def __init__(self, model_client_instance=None, **kwargs):
        """初始化Android设备管理智能体"""
        self.agent_id = AgentTypes.ANDROID_DEVICE_MANAGER.value
        self.agent_name = AGENT_NAMES[AgentTypes.ANDROID_DEVICE_MANAGER.value]
        self.platform = AgentPlatform.ANDROID
        self.model_client = model_client_instance
        self.connected_devices: Dict[str, Dict[str, Any]] = {}
        self.device_monitors: Dict[str, bool] = {}

        logger.info(f"Android设备管理智能体初始化完成: {self.agent_name}")

    @message_handler
    async def handle_message(self, message: AndroidDeviceConnectionMessage, ctx: MessageContext) -> None:
        """处理设备连接请求"""
        try:
            monitor_id = self.start_performance_monitoring()
            
            await self.send_response(f"📱 处理设备连接请求: {message.device_id}")
            
            if message.connection_type == "adb":
                result = await self._handle_adb_connection(message)
            elif message.connection_type == "wifi":
                result = await self._handle_wifi_connection(message)
            else:
                result = {
                    "success": False,
                    "error": f"不支持的连接类型: {message.connection_type}"
                }
            
            await self.send_response(
                f"✅ 设备连接处理完成: {'成功' if result['success'] else '失败'}",
                is_final=True,
                result={
                    "device_id": message.device_id,
                    "connection_result": result,
                    "metrics": self.end_performance_monitoring(monitor_id)
                }
            )

        except Exception as e:
            await self.handle_exception("handle_device_connection", e)

    async def _handle_adb_connection(self, message: AndroidDeviceConnectionMessage) -> Dict[str, Any]:
        """处理ADB连接"""
        try:
            device_id = message.device_id
            
            # 检查设备是否已连接
            if device_id in self.connected_devices:
                return {
                    "success": True,
                    "message": "设备已连接",
                    "device_info": self.connected_devices[device_id]
                }
            
            # 尝试连接设备
            device_info = await self._connect_adb_device(device_id)
            
            if device_info:
                self.connected_devices[device_id] = device_info
                # 启动设备监控
                await self._start_device_monitoring(device_id)
                
                return {
                    "success": True,
                    "message": "设备连接成功",
                    "device_info": device_info
                }
            else:
                return {
                    "success": False,
                    "error": "设备连接失败"
                }
                
        except Exception as e:
            logger.error(f"ADB连接失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _connect_adb_device(self, device_id: str) -> Optional[Dict[str, Any]]:
        """连接ADB设备"""
        try:
            # 检查设备状态
            cmd = ["adb", "-s", device_id, "get-state"] if device_id else ["adb", "get-state"]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0 or b"device" not in stdout:
                logger.error(f"设备{device_id}未连接或状态异常")
                return None
            
            # 获取设备信息
            device_info = await self._get_device_info(device_id)
            device_info["connection_type"] = "adb"
            device_info["connected_at"] = datetime.now().isoformat()
            
            return device_info
            
        except Exception as e:
            logger.error(f"连接ADB设备失败: {str(e)}")
            return None

    async def _get_device_info(self, device_id: str) -> Dict[str, Any]:
        """获取设备详细信息"""
        try:
            device_info = {
                "device_id": device_id,
                "status": "connected"
            }
            
            # 获取设备属性
            properties = [
                ("brand", "ro.product.brand"),
                ("model", "ro.product.model"),
                ("version", "ro.build.version.release"),
                ("sdk", "ro.build.version.sdk"),
                ("abi", "ro.product.cpu.abi"),
                ("density", "ro.sf.lcd_density"),
                ("resolution", "wm size")
            ]
            
            for prop_name, prop_key in properties:
                if prop_key.startswith("wm"):
                    # 特殊处理分辨率
                    cmd = ["adb", "-s", device_id, "shell", prop_key]
                else:
                    cmd = ["adb", "-s", device_id, "shell", "getprop", prop_key]
                
                try:
                    process = await asyncio.create_subprocess_exec(
                        *cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    
                    stdout, _ = await process.communicate()
                    
                    if process.returncode == 0:
                        value = stdout.decode().strip()
                        if prop_name == "resolution" and "Physical size:" in value:
                            # 解析分辨率
                            resolution_line = [line for line in value.split('\n') if 'Physical size:' in line]
                            if resolution_line:
                                resolution = resolution_line[0].split(':')[1].strip()
                                device_info[prop_name] = resolution
                        else:
                            device_info[prop_name] = value
                            
                except Exception as e:
                    logger.warning(f"获取设备属性{prop_name}失败: {str(e)}")
                    device_info[prop_name] = "unknown"
            
            return device_info
            
        except Exception as e:
            logger.error(f"获取设备信息失败: {str(e)}")
            return {"device_id": device_id, "status": "error", "error": str(e)}

    async def _handle_wifi_connection(self, message: AndroidDeviceConnectionMessage) -> Dict[str, Any]:
        """处理WiFi连接"""
        try:
            device_ip = message.device_config.get("ip_address")
            port = message.device_config.get("port", 5555)
            
            if not device_ip:
                return {
                    "success": False,
                    "error": "WiFi连接需要提供IP地址"
                }
            
            # 连接WiFi设备
            cmd = ["adb", "connect", f"{device_ip}:{port}"]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and b"connected" in stdout:
                device_id = f"{device_ip}:{port}"
                device_info = await self._get_device_info(device_id)
                device_info["connection_type"] = "wifi"
                device_info["ip_address"] = device_ip
                device_info["port"] = port
                
                self.connected_devices[device_id] = device_info
                await self._start_device_monitoring(device_id)
                
                return {
                    "success": True,
                    "message": "WiFi设备连接成功",
                    "device_info": device_info
                }
            else:
                return {
                    "success": False,
                    "error": stderr.decode() if stderr else "WiFi连接失败"
                }
                
        except Exception as e:
            logger.error(f"WiFi连接失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _start_device_monitoring(self, device_id: str):
        """启动设备监控"""
        try:
            if device_id not in self.device_monitors:
                self.device_monitors[device_id] = True
                # 启动后台监控任务
                asyncio.create_task(self._monitor_device(device_id))
                logger.info(f"启动设备监控: {device_id}")
                
        except Exception as e:
            logger.error(f"启动设备监控失败: {str(e)}")

    async def _monitor_device(self, device_id: str):
        """监控设备状态"""
        try:
            while self.device_monitors.get(device_id, False):
                # 检查设备连接状态
                cmd = ["adb", "-s", device_id, "get-state"]
                
                try:
                    process = await asyncio.create_subprocess_exec(
                        *cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    
                    stdout, stderr = await process.communicate()
                    
                    if process.returncode == 0 and b"device" in stdout:
                        # 设备正常连接
                        if device_id in self.connected_devices:
                            self.connected_devices[device_id]["last_check"] = datetime.now().isoformat()
                            self.connected_devices[device_id]["status"] = "connected"
                    else:
                        # 设备连接异常
                        if device_id in self.connected_devices:
                            self.connected_devices[device_id]["status"] = "disconnected"
                            self.connected_devices[device_id]["last_error"] = stderr.decode() if stderr else "连接丢失"
                        
                        logger.warning(f"设备{device_id}连接异常")
                        
                except Exception as e:
                    logger.error(f"监控设备{device_id}时出错: {str(e)}")
                
                # 等待30秒后再次检查
                await asyncio.sleep(30)
                
        except Exception as e:
            logger.error(f"设备监控任务异常: {str(e)}")
        finally:
            if device_id in self.device_monitors:
                del self.device_monitors[device_id]

    async def list_connected_devices(self) -> List[Dict[str, Any]]:
        """列出所有已连接的设备"""
        return list(self.connected_devices.values())

    async def disconnect_device(self, device_id: str) -> bool:
        """断开设备连接"""
        try:
            # 停止监控
            if device_id in self.device_monitors:
                self.device_monitors[device_id] = False
            
            # 从连接列表中移除
            if device_id in self.connected_devices:
                device_info = self.connected_devices[device_id]
                
                # 如果是WiFi连接，执行adb disconnect
                if device_info.get("connection_type") == "wifi":
                    cmd = ["adb", "disconnect", device_id]
                    process = await asyncio.create_subprocess_exec(
                        *cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    await process.communicate()
                
                del self.connected_devices[device_id]
                logger.info(f"设备{device_id}已断开连接")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"断开设备连接失败: {str(e)}")
            return False

    async def get_device_status(self, device_id: str) -> Optional[Dict[str, Any]]:
        """获取设备状态"""
        return self.connected_devices.get(device_id)

    async def scan_devices(self) -> List[Dict[str, Any]]:
        """扫描可用设备并自动插入数据库"""
        try:
            # 扫描真实设备
            real_devices = await self._scan_real_devices()

            if real_devices:
                logger.info(f"扫描到 {len(real_devices)} 个真实设备")
                # 将扫描到的设备插入数据库
                await self._save_devices_to_database(real_devices)
                return real_devices
            else:
                logger.warning("未找到任何连接的Android设备")
                logger.info("请确保:")
                logger.info("1. Android设备已通过USB连接到电脑")
                logger.info("2. 设备已开启USB调试模式")
                logger.info("3. 已安装Android SDK并配置ADB环境变量")
                logger.info("4. 设备已授权此电脑进行调试")
                return []

        except Exception as e:
            logger.error(f"扫描设备失败: {str(e)}")
            return []

    async def _scan_real_devices(self) -> List[Dict[str, Any]]:
        """扫描真实的Android设备"""
        try:
            # 检查ADB是否可用
            if not await self._check_adb_available():
                return []

            cmd = ["adb", "devices", "-l"]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.warning(f"ADB命令执行失败: {stderr.decode()}")
                return []

            devices = []
            lines = stdout.decode().strip().split('\n')[1:]  # 跳过标题行

            logger.info(f"ADB输出: {stdout.decode()}")

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                logger.info(f"处理设备行: {line}")

                if '\tdevice' in line or 'device ' in line:
                    # 处理不同格式的设备行
                    if '\t' in line:
                        parts = line.split('\t')
                        device_id = parts[0].strip()
                    else:
                        # 处理空格分隔的格式
                        parts = line.split()
                        if len(parts) >= 2 and parts[1] == 'device':
                            device_id = parts[0].strip()
                        else:
                            continue

                    if device_id and device_id != "List":
                        logger.info(f"发现设备: {device_id}")
                        # 获取详细设备信息
                        device_info = await self._get_detailed_device_info(device_id)
                        devices.append(device_info)
                elif '\tunauthorized' in line:
                    device_id = line.split('\t')[0].strip()
                    logger.warning(f"设备 {device_id} 未授权，请在设备上确认USB调试授权")
                elif '\toffline' in line:
                    device_id = line.split('\t')[0].strip()
                    logger.warning(f"设备 {device_id} 离线")

            logger.info(f"成功扫描到 {len(devices)} 个可用设备")
            return devices

        except FileNotFoundError:
            logger.error("ADB命令未找到")
            logger.error("请安装Android SDK并确保adb在系统PATH中")
            logger.error("或者下载ADB工具: https://developer.android.com/studio/releases/platform-tools")
            return []
        except Exception as e:
            logger.error(f"扫描真实设备失败: {str(e)}")
            return []

    async def _check_adb_available(self) -> bool:
        """检查ADB是否可用"""
        try:
            process = await asyncio.create_subprocess_exec(
                "adb", "version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                version_info = stdout.decode().strip()
                logger.info(f"ADB可用: {version_info.split()[0]} {version_info.split()[4]}")
                return True
            else:
                logger.error(f"ADB版本检查失败: {stderr.decode()}")
                return False

        except FileNotFoundError:
            logger.error("ADB未安装或未在PATH中")
            return False
        except Exception as e:
            logger.error(f"检查ADB可用性失败: {e}")
            return False

    async def _get_detailed_device_info(self, device_id: str) -> Dict[str, Any]:
        """获取设备详细信息"""
        try:
            logger.info(f"获取设备详细信息: {device_id}")

            # 获取设备属性
            props = await self._get_device_properties(device_id)

            # 获取屏幕信息
            screen_resolution = await self._get_screen_resolution(device_id)
            screen_density = await self._get_screen_density(device_id)

            # 获取内存信息
            memory_info = await self._get_memory_info(device_id)

            # 构建设备信息 - 优先使用品牌+型号
            brand = props.get("ro.product.brand", "")
            model = props.get("ro.product.model", "")
            manufacturer = props.get("ro.product.manufacturer", "Unknown")

            # 生成设备名称：品牌 + 型号
            if brand and model:
                # 如果型号已经包含品牌，直接使用型号
                if brand.lower() in model.lower():
                    device_name = model
                else:
                    device_name = f"{brand} {model}"
            elif manufacturer and model:
                # 如果型号已经包含制造商，直接使用型号
                if manufacturer.lower() in model.lower():
                    device_name = model
                else:
                    device_name = f"{manufacturer} {model}"
            elif model:
                device_name = model
            elif brand:
                device_name = f"{brand} Device"
            elif manufacturer != "Unknown":
                device_name = f"{manufacturer} Device"
            else:
                device_name = f"Android Device {device_id[:8]}"

            device_info = {
                "device_id": device_id,
                "device_name": device_name,
                "device_model": model or "Unknown",
                "manufacturer": manufacturer,
                "android_version": props.get("ro.build.version.release", "Unknown"),
                "api_level": self._safe_int(props.get("ro.build.version.sdk", "0")),
                "build_version": props.get("ro.build.display.id", "Unknown"),
                "screen_resolution": screen_resolution,
                "screen_density": screen_density,
                "cpu_architecture": props.get("ro.product.cpu.abi", "Unknown"),
                "memory_total": memory_info,
                "connection_type": "wifi" if ":" in device_id else "usb",
                "ip_address": device_id.split(":")[0] if ":" in device_id else None,
                "adb_port": int(device_id.split(":")[1]) if ":" in device_id else None,
                "status": "online",
                "last_seen": datetime.now(),
                "capabilities": ["screenshot", "ui_hierarchy", "app_management"],
                "device_config": {
                    "supports_uiautomator": True,
                    "supports_appium": True,
                    "supports_screenshot": True,
                    "is_emulator": "emulator" in device_id.lower()
                }
            }

            logger.info(f"设备信息获取成功: {device_name} ({device_id})")
            return device_info

        except Exception as e:
            logger.error(f"获取设备详细信息失败: {device_id}, 错误: {e}")
            return {
                "device_id": device_id,
                "device_name": f"Android Device {device_id[:8]}",
                "device_model": "Unknown",
                "manufacturer": "Unknown",
                "android_version": "Unknown",
                "connection_type": "wifi" if ":" in device_id else "usb",
                "status": "online",
                "last_seen": datetime.now()
            }

    def _safe_int(self, value: str) -> Optional[int]:
        """安全转换为整数"""
        try:
            return int(value) if value and value != "0" else None
        except (ValueError, TypeError):
            return None

    async def _get_device_properties(self, device_id: str) -> Dict[str, str]:
        """获取设备属性"""
        try:
            # 获取关键属性
            key_props = [
                "ro.product.model",
                "ro.product.manufacturer",
                "ro.product.brand",
                "ro.product.name",
                "ro.product.device",
                "ro.build.version.release",
                "ro.build.version.sdk",
                "ro.build.display.id",
                "ro.sf.lcd_density",
                "ro.product.cpu.abi"
            ]

            props = {}

            for prop in key_props:
                try:
                    cmd = ["adb", "-s", device_id, "shell", "getprop", prop]

                    process = await asyncio.create_subprocess_exec(
                        *cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )

                    stdout, stderr = await process.communicate()

                    if process.returncode == 0:
                        value = stdout.decode().strip()
                        if value:  # 只保存非空值
                            props[prop] = value

                except Exception as e:
                    logger.debug(f"获取属性 {prop} 失败: {e}")
                    continue

            logger.info(f"获取到设备属性: {props}")
            return props

        except Exception as e:
            logger.error(f"获取设备属性失败: {device_id}, 错误: {e}")
            return {}

    async def _get_screen_resolution(self, device_id: str) -> str:
        """获取屏幕分辨率"""
        try:
            cmd = ["adb", "-s", device_id, "shell", "wm", "size"]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                output = stdout.decode().strip()
                if "Physical size:" in output:
                    return output.split("Physical size: ")[1]
                elif "size:" in output:
                    return output.split("size: ")[1]

            return "Unknown"

        except Exception as e:
            logger.debug(f"获取屏幕分辨率失败 {device_id}: {e}")
            return "Unknown"

    async def _get_screen_density(self, device_id: str) -> str:
        """获取屏幕密度"""
        try:
            cmd = ["adb", "-s", device_id, "shell", "wm", "density"]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                output = stdout.decode().strip()
                if "Physical density:" in output:
                    return output.split("Physical density: ")[1]
                elif "density:" in output:
                    return output.split("density: ")[1]

            return "Unknown"

        except Exception as e:
            logger.debug(f"获取屏幕密度失败 {device_id}: {e}")
            return "Unknown"

    async def _get_memory_info(self, device_id: str) -> str:
        """获取内存信息"""
        try:
            cmd = ["adb", "-s", device_id, "shell", "cat", "/proc/meminfo"]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                output = stdout.decode().strip()
                for line in output.split('\n'):
                    if line.startswith('MemTotal:'):
                        # 提取内存大小，转换为MB
                        mem_kb = int(line.split()[1])
                        mem_mb = mem_kb // 1024
                        return f"{mem_mb}MB"

            return "Unknown"

        except Exception as e:
            logger.debug(f"获取内存信息失败 {device_id}: {e}")
            return "Unknown"

    async def _save_devices_to_database(self, devices: List[Dict[str, Any]]) -> None:
        """将扫描到的设备保存到数据库"""
        try:
            from app.database.connection import get_database
            from app.database.models.android_analysis import AndroidDevice
            from sqlalchemy import select

            async for session in get_database():
                saved_count = 0
                updated_count = 0

                for device_data in devices:
                    try:
                        # 检查设备是否已存在
                        stmt = select(AndroidDevice).where(AndroidDevice.device_id == device_data["device_id"])
                        result = await session.execute(stmt)
                        existing_device = result.scalar_one_or_none()

                        if existing_device:
                            # 更新现有设备信息
                            for key, value in device_data.items():
                                if hasattr(existing_device, key):
                                    setattr(existing_device, key, value)
                            existing_device.last_seen = datetime.now()
                            updated_count += 1
                            logger.info(f"更新设备: {device_data['device_name']} ({device_data['device_id']})")
                        else:
                            # 插入新设备
                            device = AndroidDevice(**device_data)
                            session.add(device)
                            saved_count += 1
                            logger.info(f"新增设备: {device_data['device_name']} ({device_data['device_id']})")

                    except Exception as e:
                        logger.error(f"保存设备失败 {device_data.get('device_id', 'unknown')}: {e}")
                        continue

                # 提交更改
                await session.commit()
                logger.info(f"设备数据库更新完成: 新增 {saved_count} 个，更新 {updated_count} 个设备")
                break  # 只需要一个会话

        except Exception as e:
            logger.error(f"保存设备到数据库失败: {e}")



    async def install_app(self, device_id: str, apk_path: str) -> Dict[str, Any]:
        """在设备上安装应用"""
        try:
            cmd = ["adb", "-s", device_id, "install", "-r", apk_path]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            success = process.returncode == 0 and b"Success" in stdout
            
            return {
                "success": success,
                "output": stdout.decode() if stdout else "",
                "error": stderr.decode() if stderr else ""
            }
            
        except Exception as e:
            logger.error(f"安装应用失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def uninstall_app(self, device_id: str, package_name: str) -> Dict[str, Any]:
        """卸载设备上的应用"""
        try:
            cmd = ["adb", "-s", device_id, "uninstall", package_name]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            success = process.returncode == 0 and b"Success" in stdout
            
            return {
                "success": success,
                "output": stdout.decode() if stdout else "",
                "error": stderr.decode() if stderr else ""
            }
            
        except Exception as e:
            logger.error(f"卸载应用失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
