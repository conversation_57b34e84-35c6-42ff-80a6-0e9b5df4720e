"""
图像处理工具模块
提供图像编码、解码、压缩等功能
"""
import base64
import io
from pathlib import Path
from typing import Optional, Tuple
from PIL import Image
from loguru import logger


def encode_image_to_base64(image_path: str) -> str:
    """
    将图像文件编码为base64字符串
    
    Args:
        image_path: 图像文件路径
    
    Returns:
        base64编码的字符串
    """
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
        
        logger.info(f"图像编码成功: {image_path}")
        return encoded_string
        
    except Exception as e:
        logger.error(f"图像编码失败: {image_path}, 错误: {e}")
        raise


def decode_base64_to_image(base64_string: str, output_path: str) -> str:
    """
    将base64字符串解码为图像文件
    
    Args:
        base64_string: base64编码的字符串
        output_path: 输出文件路径
    
    Returns:
        保存的文件路径
    """
    try:
        # 移除可能的data URL前缀
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        # 解码base64
        image_data = base64.b64decode(base64_string)
        
        # 确保输出目录存在
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 保存图像
        with open(output_path, "wb") as image_file:
            image_file.write(image_data)
        
        logger.info(f"图像解码成功: {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"图像解码失败: {e}")
        raise


def compress_image(image_path: str, output_path: Optional[str] = None, 
                  quality: int = 85, max_size: Optional[Tuple[int, int]] = None) -> str:
    """
    压缩图像
    
    Args:
        image_path: 输入图像路径
        output_path: 输出图像路径（可选）
        quality: 压缩质量（1-100）
        max_size: 最大尺寸（宽度，高度）
    
    Returns:
        压缩后的图像路径
    """
    try:
        if output_path is None:
            output_path = image_path
        
        # 打开图像
        with Image.open(image_path) as img:
            # 转换为RGB模式（如果需要）
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # 调整尺寸（如果指定）
            if max_size:
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存压缩后的图像
            img.save(output_path, 'JPEG', quality=quality, optimize=True)
        
        logger.info(f"图像压缩成功: {image_path} -> {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"图像压缩失败: {image_path}, 错误: {e}")
        raise


def get_image_info(image_path: str) -> dict:
    """
    获取图像信息
    
    Args:
        image_path: 图像文件路径
    
    Returns:
        图像信息字典
    """
    try:
        with Image.open(image_path) as img:
            info = {
                'width': img.width,
                'height': img.height,
                'mode': img.mode,
                'format': img.format,
                'size_bytes': Path(image_path).stat().st_size
            }
        
        logger.debug(f"获取图像信息: {image_path} - {info}")
        return info
        
    except Exception as e:
        logger.error(f"获取图像信息失败: {image_path}, 错误: {e}")
        raise


def validate_image(image_path: str) -> bool:
    """
    验证图像文件是否有效
    
    Args:
        image_path: 图像文件路径
    
    Returns:
        是否为有效图像
    """
    try:
        with Image.open(image_path) as img:
            img.verify()
        return True
        
    except Exception as e:
        logger.warning(f"图像验证失败: {image_path}, 错误: {e}")
        return False


def convert_image_format(image_path: str, output_path: str, target_format: str = 'PNG') -> str:
    """
    转换图像格式
    
    Args:
        image_path: 输入图像路径
        output_path: 输出图像路径
        target_format: 目标格式（PNG, JPEG, WEBP等）
    
    Returns:
        转换后的图像路径
    """
    try:
        with Image.open(image_path) as img:
            # 转换为RGB模式（如果目标格式是JPEG）
            if target_format.upper() == 'JPEG' and img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存为目标格式
            img.save(output_path, target_format.upper())
        
        logger.info(f"图像格式转换成功: {image_path} -> {output_path} ({target_format})")
        return output_path
        
    except Exception as e:
        logger.error(f"图像格式转换失败: {image_path}, 错误: {e}")
        raise


def create_thumbnail(image_path: str, output_path: str, size: Tuple[int, int] = (200, 200)) -> str:
    """
    创建缩略图
    
    Args:
        image_path: 输入图像路径
        output_path: 输出缩略图路径
        size: 缩略图尺寸
    
    Returns:
        缩略图路径
    """
    try:
        with Image.open(image_path) as img:
            # 创建缩略图
            img.thumbnail(size, Image.Resampling.LANCZOS)
            
            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存缩略图
            img.save(output_path)
        
        logger.info(f"缩略图创建成功: {image_path} -> {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"缩略图创建失败: {image_path}, 错误: {e}")
        raise


def image_to_base64_data_url(image_path: str) -> str:
    """
    将图像转换为data URL格式的base64字符串
    
    Args:
        image_path: 图像文件路径
    
    Returns:
        data URL格式的base64字符串
    """
    try:
        # 获取文件扩展名
        ext = Path(image_path).suffix.lower()
        
        # 确定MIME类型
        mime_type = {
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.bmp': 'image/bmp'
        }.get(ext, 'image/png')
        
        # 编码为base64
        base64_string = encode_image_to_base64(image_path)
        
        # 创建data URL
        data_url = f"data:{mime_type};base64,{base64_string}"
        
        logger.info(f"图像转换为data URL成功: {image_path}")
        return data_url
        
    except Exception as e:
        logger.error(f"图像转换为data URL失败: {image_path}, 错误: {e}")
        raise
