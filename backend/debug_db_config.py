#!/usr/bin/env python3
"""
调试数据库配置脚本
用于检查当前的数据库配置和连接
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def debug_database_config():
    """调试数据库配置"""
    print("🔍 调试数据库配置...")
    
    try:
        # 检查环境变量
        import os
        print("\n📋 环境变量:")
        for key in os.environ:
            if any(keyword in key.upper() for keyword in ['DATABASE', 'MYSQL', 'DB']):
                print(f"  {key}: {os.environ[key]}")
        
        # 检查配置类
        from app.core.config import get_settings
        settings = get_settings()
        
        print("\n🔧 配置类设置:")
        print(f"  DATABASE_URL: {settings.DATABASE_URL}")
        print(f"  MYSQL_HOST: {settings.MYSQL_HOST}")
        print(f"  MYSQL_PORT: {settings.MYSQL_PORT}")
        print(f"  MYSQL_USER: {settings.MYSQL_USER}")
        print(f"  MYSQL_PASSWORD: {settings.MYSQL_PASSWORD}")
        print(f"  MYSQL_DATABASE: {settings.MYSQL_DATABASE}")
        
        print(f"\n🔗 最终数据库URL:")
        print(f"  database_url: {settings.database_url}")
        print(f"  mysql_database_url: {settings.mysql_database_url}")
        
        # 检查数据库管理器
        from app.database.connection import db_manager
        print(f"\n🏭 数据库管理器状态:")
        print(f"  已初始化: {db_manager._initialized}")
        if hasattr(db_manager, 'engine') and db_manager.engine:
            print(f"  引擎URL: {db_manager.engine.url}")
        
        # 测试连接
        print(f"\n🧪 测试数据库连接...")
        await db_manager.initialize()
        
        async with db_manager.get_session() as session:
            from sqlalchemy import text
            result = await session.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            
            if row:
                print(f"✅ 数据库连接成功!")
                print(f"   测试查询结果: {row.test}")
            else:
                print("❌ 数据库连接失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理连接
        try:
            await db_manager.close()
        except:
            pass

async def test_page_analysis_api():
    """测试页面分析API的数据库连接"""
    print("\n🔍 测试页面分析API数据库连接...")
    
    try:
        # 导入相关模块
        from app.database.repositories.page_analysis_repository import PageAnalysisRepository
        from app.database.connection import get_database
        
        # 创建仓库实例
        repo = PageAnalysisRepository()
        
        # 测试数据库连接
        async for session in get_database():
            from sqlalchemy import select, desc
            from app.database.models.page_analysis import PageAnalysisResult
            
            query = select(PageAnalysisResult).order_by(desc(PageAnalysisResult.created_at))
            result = await session.execute(query.limit(1))
            pages = result.scalars().all()
            
            print(f"✅ 页面分析API数据库连接成功!")
            print(f"   查询到 {len(pages)} 条记录")
            break
        
        return True
        
    except Exception as e:
        print(f"❌ 页面分析API数据库连接失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始调试数据库配置...")
    
    # 调试基本配置
    config_ok = await debug_database_config()
    
    if config_ok:
        # 测试页面分析API
        await test_page_analysis_api()
    
    print("\n✅ 调试完成")

if __name__ == "__main__":
    asyncio.run(main())
