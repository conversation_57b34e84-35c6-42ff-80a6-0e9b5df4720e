#!/usr/bin/env python3
"""
更新数据库中设备的名称为品牌+型号格式
"""
import asyncio
import sys
import os
from sqlalchemy import select

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.connection import get_database
from app.database.models.android_analysis import AndroidDevice
from app.agents.android.android_device_manager_agent import AndroidDeviceManagerAgent

async def update_device_names():
    """更新设备名称"""
    try:
        print("🔄 开始更新设备名称...")
        
        # 创建设备管理智能体
        agent = AndroidDeviceManagerAgent()
        
        # 重新扫描设备以获取最新信息
        print("📱 重新扫描设备...")
        devices = await agent.scan_devices()
        
        if devices:
            print(f"✅ 扫描完成，发现 {len(devices)} 个设备")
            for device in devices:
                print(f"  - {device['device_name']} ({device['device_id']})")
                print(f"    制造商: {device['manufacturer']}")
                print(f"    型号: {device['device_model']}")
                print(f"    Android版本: {device['android_version']}")
        else:
            print("❌ 未发现设备")
        
        # 查询数据库中的设备
        print("\n💾 查询数据库中的设备...")
        async for session in get_database():
            stmt = select(AndroidDevice)
            result = await session.execute(stmt)
            db_devices = result.scalars().all()
            
            print(f"📊 数据库中有 {len(db_devices)} 个设备")
            
            for device in db_devices:
                print(f"  - {device.device_name} ({device.device_id})")
                print(f"    制造商: {device.manufacturer}")
                print(f"    型号: {device.device_model}")
            
            break  # 只需要一个会话
        
    except Exception as e:
        print(f"❌ 更新设备名称失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 更新Android设备名称...")
    asyncio.run(update_device_names())
