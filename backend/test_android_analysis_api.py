#!/usr/bin/env python3
"""
测试Android分析API和SSE流
"""
import asyncio
import aiohttp
import json
import base64
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_android_analysis_api():
    """测试Android分析API"""
    print("🧪 测试Android分析API...")
    
    # 创建一个简单的测试图片数据（1x1像素的PNG）
    test_image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    # 准备请求数据
    request_data = {
        "session_id": f"test_android_analysis_{int(asyncio.get_event_loop().time())}",
        "screenshot_data": test_image_data,
        "device_id": "13764254B4001229",  # 使用真实设备ID
        "package_name": "com.example.test",
        "activity_name": "MainActivity",
        "test_description": "测试Android界面分析",
        "additional_context": "这是一个测试分析",
        "generate_formats": ["appium"],
        "include_non_interactive": False,
        "focus_areas": ["buttons", "inputs"]
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print("📤 发送分析请求...")
            
            # 发送分析请求
            async with session.post(
                'http://localhost:8000/api/v1/android/analysis/analyze',
                json=request_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 分析请求成功")
                    print(f"   会话ID: {data.get('session_id')}")
                    print(f"   流URL: {data.get('stream_url')}")
                    
                    session_id = data.get('session_id')
                    
                    if session_id:
                        print(f"\n📡 测试SSE流连接...")
                        await test_sse_stream(session, session_id)
                    
                else:
                    error_text = await response.text()
                    print(f"❌ 分析请求失败: {response.status}")
                    print(f"   错误信息: {error_text}")
                    
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def test_sse_stream(session, session_id):
    """测试SSE流连接"""
    try:
        stream_url = f'http://localhost:8000/api/v1/android/analysis/stream/{session_id}'
        print(f"🔗 连接SSE流: {stream_url}")
        
        async with session.get(stream_url) as response:
            if response.status == 200:
                print("✅ SSE连接成功")
                
                # 读取前几个事件
                event_count = 0
                max_events = 10
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('data: '):
                        event_count += 1
                        data_part = line[6:]  # 移除 'data: ' 前缀
                        
                        try:
                            event_data = json.loads(data_part)
                            print(f"📨 事件 {event_count}: {event_data.get('type', 'unknown')}")
                            
                            if event_data.get('content'):
                                print(f"   内容: {event_data['content'][:100]}...")
                                
                        except json.JSONDecodeError:
                            print(f"📨 事件 {event_count}: {data_part[:100]}...")
                    
                    elif line.startswith('event: '):
                        event_type = line[7:]  # 移除 'event: ' 前缀
                        print(f"🏷️ 事件类型: {event_type}")
                    
                    # 限制读取的事件数量
                    if event_count >= max_events:
                        print(f"\n⏹️ 已读取 {max_events} 个事件，停止测试")
                        break
                        
            else:
                error_text = await response.text()
                print(f"❌ SSE连接失败: {response.status}")
                print(f"   错误信息: {error_text}")
                
    except Exception as e:
        print(f"❌ SSE流测试失败: {e}")

async def test_backend_health():
    """测试后端健康状态"""
    print("🏥 检查后端健康状态...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/api/v1/system/health') as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ 后端服务正常")
                    print(f"   状态: {data.get('status')}")
                    print(f"   时间: {data.get('timestamp')}")
                else:
                    print(f"❌ 后端健康检查失败: {response.status}")
    except Exception as e:
        print(f"❌ 无法连接后端: {e}")

async def main():
    """主测试函数"""
    print("🚀 Android分析API测试")
    print("=" * 50)
    
    # 1. 检查后端健康状态
    await test_backend_health()
    
    print("\n" + "-" * 50)
    
    # 2. 测试Android分析API
    await test_android_analysis_api()
    
    print("\n" + "=" * 50)
    print("📋 测试完成")
    print("💡 建议: 访问 http://localhost:3001/android/analysis 测试前端界面")

if __name__ == "__main__":
    asyncio.run(main())
